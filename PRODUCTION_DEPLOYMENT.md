# DeepSearch 生产环境部署指南

## 项目概述

DeepSearch 是一个 AI 驱动的代码搜索和问答系统，包含以下组件：
- **前端 (web)**: React + Vite 应用
- **后端 (server)**: NestJS API 服务
- **本地代理 (local-agent)**: kwaipilot-binary 二进制服务
- **共享包 (packages/shared)**: 通用类型定义

## 系统要求

- **Node.js**: >= 18.0.0
- **pnpm**: >= 9.0.0
- **操作系统**: Linux/macOS/Windows
- **内存**: 建议 >= 4GB
- **磁盘空间**: >= 10GB

## 部署方式

### 方式一：传统部署（推荐用于生产环境）

#### 1. 环境准备

```bash
# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 pnpm
npm install -g pnpm@latest

# 创建部署目录
sudo mkdir -p /opt/deepsearch
sudo chown $USER:$USER /opt/deepsearch
cd /opt/deepsearch
```

#### 2. 代码部署

```bash
# 克隆代码
git clone <your-repo-url> .

# 安装依赖
pnpm install

# 安装基础组件和二进制文件
pnpm install:base

# 构建项目
pnpm build
```

#### 3. 环境变量配置

创建生产环境配置文件：

```bash
# 创建环境变量文件
cat > .env.production << 'EOF'
# 服务配置
NODE_ENV=production
PORT=3001
LOG_LEVEL=info

# CORS 配置
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# 二进制服务配置
BINARY_PATH=/opt/deepsearch/local-agent/kwaipilot-binary
BINARY_CWD=/opt/deepsearch/local-agent

# 代理配置
REPO_PATH=/opt/deepsearch/repos
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=https://kwaipilot.corp.kuaishou.com/
AGENT_PREFERENCE=intelligent
USER_NAME=production

# 健康检查配置
HEALTH_CHECK_INTERVAL=600000
HEALTH_CHECK_TIMEOUT=180000

# 重启配置
MAX_RESTART_ATTEMPTS=10
RESTART_DELAY=5000
EOF
```

#### 4. 创建启动脚本

```bash
# 创建启动脚本
cat > start-production.sh << 'EOF'
#!/bin/bash

# 设置工作目录
cd /opt/deepsearch

# 加载环境变量
export $(cat .env.production | xargs)

# 启动后端服务
echo "Starting DeepSearch server..."
cd server
NODE_ENV=production node dist/server/src/main.js &
SERVER_PID=$!

# 启动前端服务 (使用 nginx 或其他 web 服务器)
echo "Frontend should be served by nginx or other web server"

# 保存 PID
echo $SERVER_PID > /opt/deepsearch/server.pid

echo "DeepSearch started successfully!"
echo "Server PID: $SERVER_PID"
echo "Server running on port: $PORT"
EOF

chmod +x start-production.sh
```

#### 5. 创建停止脚本

```bash
cat > stop-production.sh << 'EOF'
#!/bin/bash

echo "Stopping DeepSearch..."

# 停止服务器
if [ -f /opt/deepsearch/server.pid ]; then
    SERVER_PID=$(cat /opt/deepsearch/server.pid)
    if ps -p $SERVER_PID > /dev/null; then
        kill $SERVER_PID
        echo "Server stopped (PID: $SERVER_PID)"
    fi
    rm -f /opt/deepsearch/server.pid
fi

# 停止相关进程
pkill -f "kwaipilot-binary"
pkill -f "deepsearch"

echo "DeepSearch stopped successfully!"
EOF

chmod +x stop-production.sh
```

#### 6. 配置 Nginx（前端静态文件服务）

```bash
# 安装 nginx
sudo apt-get install nginx

# 创建 nginx 配置
sudo cat > /etc/nginx/sites-available/deepsearch << 'EOF'
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    
    # 前端静态文件
    location / {
        root /opt/deepsearch/web/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理到后端
    location /api/ {
        proxy_pass http://localhost:3001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # WebSocket 支持
    location /socket.io/ {
        proxy_pass http://localhost:3001/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/deepsearch /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 7. 创建 Systemd 服务

```bash
sudo cat > /etc/systemd/system/deepsearch.service << 'EOF'
[Unit]
Description=DeepSearch AI Code Search Service
After=network.target

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/opt/deepsearch
ExecStart=/opt/deepsearch/start-production.sh
ExecStop=/opt/deepsearch/stop-production.sh
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# 重载 systemd 并启用服务
sudo systemctl daemon-reload
sudo systemctl enable deepsearch
```

#### 8. 启动服务

```bash
# 启动服务
sudo systemctl start deepsearch

# 检查状态
sudo systemctl status deepsearch

# 查看日志
sudo journalctl -u deepsearch -f
```

### 方式二：Docker 部署

#### 1. 创建 Dockerfile

```bash
cat > Dockerfile << 'EOF'
# 多阶段构建
FROM node:18-alpine AS builder

# 安装 pnpm
RUN npm install -g pnpm

WORKDIR /app

# 复制 package 文件
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY server/package.json ./server/
COPY web/package.json ./web/
COPY packages/shared/package.json ./packages/shared/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建项目
RUN pnpm build

# 生产镜像
FROM node:18-alpine AS production

# 安装 pnpm
RUN npm install -g pnpm

WORKDIR /app

# 复制构建产物
COPY --from=builder /app/server/dist ./server/dist
COPY --from=builder /app/web/dist ./web/dist
COPY --from=builder /app/local-agent ./local-agent
COPY --from=builder /app/build ./build
COPY --from=builder /app/server/package.json ./server/
COPY --from=builder /app/packages ./packages
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/server/node_modules ./server/node_modules

# 创建必要目录
RUN mkdir -p /app/repos

# 设置权限
RUN chmod +x /app/local-agent/kwaipilot-binary

# 暴露端口
EXPOSE 3001

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3001

# 启动命令
CMD ["node", "server/dist/server/src/main.js"]
EOF
```

#### 2. 创建 docker-compose.yml

```bash
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  deepsearch-server:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - CORS_ORIGIN=*
      - BINARY_PATH=/app/local-agent/kwaipilot-binary
      - BINARY_CWD=/app/local-agent
      - REPO_PATH=/app/repos
    volumes:
      - ./repos:/app/repos
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./web/dist:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - deepsearch-server
    restart: unless-stopped

volumes:
  repos:
  data:
EOF
```

#### 3. 创建 nginx 配置

```bash
cat > nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    upstream deepsearch_backend {
        server deepsearch-server:3001;
    }

    server {
        listen 80;
        server_name localhost;

        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
        }

        location /api/ {
            proxy_pass http://deepsearch_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /socket.io/ {
            proxy_pass http://deepsearch_backend/socket.io/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
EOF
```

#### 4. Docker 部署命令

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart
```

## 监控和维护

### 1. 日志管理

```bash
# 查看服务日志
sudo journalctl -u deepsearch -f

# 或者 Docker 日志
docker-compose logs -f deepsearch-server
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:3001/health

# 检查前端
curl http://localhost/
```

### 3. 备份策略

```bash
# 备份数据目录
tar -czf deepsearch-backup-$(date +%Y%m%d).tar.gz /opt/deepsearch/data /opt/deepsearch/repos

# 定期备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/deepsearch"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/deepsearch-$DATE.tar.gz /opt/deepsearch/data /opt/deepsearch/repos

# 保留最近7天的备份
find $BACKUP_DIR -name "deepsearch-*.tar.gz" -mtime +7 -delete
EOF

# 添加到 crontab
echo "0 2 * * * /opt/deepsearch/backup.sh" | crontab -
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口是否被占用：`netstat -tlnp | grep 3001`
   - 检查环境变量配置
   - 查看日志文件

2. **前端无法访问后端**
   - 检查 CORS 配置
   - 确认 nginx 代理配置正确
   - 检查防火墙设置

3. **二进制服务异常**
   - 确认 kwaipilot-binary 文件权限
   - 检查系统架构匹配
   - 查看相关进程状态

### 性能优化

1. **启用 gzip 压缩**
2. **配置 CDN**
3. **数据库索引优化**
4. **缓存策略**

## 安全建议

1. **使用 HTTPS**
2. **配置防火墙**
3. **定期更新依赖**
4. **访问控制**
5. **日志审计**

## 快速部署脚本

为了简化部署过程，我为你创建了一键部署脚本：

### 自动化部署脚本

```bash
cat > deploy.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 开始部署 DeepSearch 到生产环境..."

# 检查系统要求
check_requirements() {
    echo "📋 检查系统要求..."

    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi

    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo "❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi

    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        echo "📦 安装 pnpm..."
        npm install -g pnpm@latest
    fi

    echo "✅ 系统要求检查通过"
}

# 创建部署目录
setup_directories() {
    echo "📁 创建部署目录..."

    DEPLOY_DIR="/opt/deepsearch"
    sudo mkdir -p $DEPLOY_DIR
    sudo chown $USER:$USER $DEPLOY_DIR

    mkdir -p $DEPLOY_DIR/logs
    mkdir -p $DEPLOY_DIR/data
    mkdir -p $DEPLOY_DIR/repos

    echo "✅ 目录创建完成"
}

# 部署代码
deploy_code() {
    echo "📥 部署代码..."

    cd /opt/deepsearch

    # 如果是 git 仓库，拉取最新代码
    if [ -d ".git" ]; then
        git pull origin main
    else
        echo "⚠️  请手动将代码复制到 /opt/deepsearch 目录"
        echo "或者使用 git clone 命令克隆代码"
        exit 1
    fi

    echo "✅ 代码部署完成"
}

# 安装依赖和构建
build_project() {
    echo "🔨 安装依赖和构建项目..."

    cd /opt/deepsearch

    # 安装依赖
    pnpm install --frozen-lockfile

    # 安装基础组件
    pnpm install:base

    # 构建项目
    pnpm build

    echo "✅ 项目构建完成"
}

# 配置环境变量
setup_environment() {
    echo "⚙️  配置环境变量..."

    cat > /opt/deepsearch/.env.production << 'ENVEOF'
NODE_ENV=production
PORT=3001
LOG_LEVEL=info
CORS_ORIGIN=*
CORS_CREDENTIALS=false
BINARY_PATH=/opt/deepsearch/local-agent/kwaipilot-binary
BINARY_CWD=/opt/deepsearch/local-agent
REPO_PATH=/opt/deepsearch/repos
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=https://kwaipilot.corp.kuaishou.com/
AGENT_PREFERENCE=intelligent
USER_NAME=production
HEALTH_CHECK_INTERVAL=600000
HEALTH_CHECK_TIMEOUT=180000
MAX_RESTART_ATTEMPTS=10
RESTART_DELAY=5000
ENVEOF

    echo "✅ 环境变量配置完成"
}

# 配置 Nginx
setup_nginx() {
    echo "🌐 配置 Nginx..."

    # 安装 nginx
    if ! command -v nginx &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y nginx
    fi

    # 创建 nginx 配置
    sudo tee /etc/nginx/sites-available/deepsearch > /dev/null << 'NGINXEOF'
server {
    listen 80;
    server_name _;

    # 前端静态文件
    location / {
        root /opt/deepsearch/web/dist;
        try_files $uri $uri/ /index.html;

        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # WebSocket 支持
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3001/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:3001/health;
        access_log off;
    }
}
NGINXEOF

    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/deepsearch /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default

    # 测试配置
    sudo nginx -t

    echo "✅ Nginx 配置完成"
}

# 创建 systemd 服务
setup_systemd() {
    echo "🔧 配置 systemd 服务..."

    sudo tee /etc/systemd/system/deepsearch.service > /dev/null << 'SERVICEEOF'
[Unit]
Description=DeepSearch AI Code Search Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/deepsearch/server
ExecStart=/usr/bin/node dist/server/src/main.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
EnvironmentFile=/opt/deepsearch/.env.production
StandardOutput=journal
StandardError=journal
SyslogIdentifier=deepsearch

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/deepsearch

[Install]
WantedBy=multi-user.target
SERVICEEOF

    # 设置权限
    sudo chown -R www-data:www-data /opt/deepsearch
    sudo chmod +x /opt/deepsearch/local-agent/kwaipilot-binary

    # 重载 systemd
    sudo systemctl daemon-reload
    sudo systemctl enable deepsearch
    sudo systemctl enable nginx

    echo "✅ systemd 服务配置完成"
}

# 启动服务
start_services() {
    echo "🚀 启动服务..."

    # 启动 DeepSearch
    sudo systemctl start deepsearch

    # 启动 Nginx
    sudo systemctl start nginx

    # 检查状态
    sleep 5

    if sudo systemctl is-active --quiet deepsearch; then
        echo "✅ DeepSearch 服务启动成功"
    else
        echo "❌ DeepSearch 服务启动失败"
        sudo systemctl status deepsearch
        exit 1
    fi

    if sudo systemctl is-active --quiet nginx; then
        echo "✅ Nginx 服务启动成功"
    else
        echo "❌ Nginx 服务启动失败"
        sudo systemctl status nginx
        exit 1
    fi
}

# 创建管理脚本
create_management_scripts() {
    echo "📝 创建管理脚本..."

    # 状态检查脚本
    cat > /opt/deepsearch/status.sh << 'STATUSEOF'
#!/bin/bash
echo "=== DeepSearch 服务状态 ==="
echo "DeepSearch 服务:"
sudo systemctl status deepsearch --no-pager -l
echo ""
echo "Nginx 服务:"
sudo systemctl status nginx --no-pager -l
echo ""
echo "端口监听:"
netstat -tlnp | grep -E ':(80|3001) '
echo ""
echo "健康检查:"
curl -s http://localhost:3001/health || echo "健康检查失败"
STATUSEOF

    # 日志查看脚本
    cat > /opt/deepsearch/logs.sh << 'LOGSEOF'
#!/bin/bash
echo "=== DeepSearch 日志 ==="
sudo journalctl -u deepsearch -f --no-pager
LOGSEOF

    # 重启脚本
    cat > /opt/deepsearch/restart.sh << 'RESTARTEOF'
#!/bin/bash
echo "重启 DeepSearch 服务..."
sudo systemctl restart deepsearch
sudo systemctl restart nginx
echo "服务重启完成"
./status.sh
RESTARTEOF

    # 备份脚本
    cat > /opt/deepsearch/backup.sh << 'BACKUPEOF'
#!/bin/bash
BACKUP_DIR="/backup/deepsearch"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/deepsearch-$DATE.tar.gz \
    /opt/deepsearch/data \
    /opt/deepsearch/repos \
    /opt/deepsearch/.env.production

echo "备份完成: $BACKUP_DIR/deepsearch-$DATE.tar.gz"

# 保留最近7天的备份
find $BACKUP_DIR -name "deepsearch-*.tar.gz" -mtime +7 -delete
BACKUPEOF

    chmod +x /opt/deepsearch/*.sh

    echo "✅ 管理脚本创建完成"
}

# 主函数
main() {
    echo "🎯 DeepSearch 生产环境自动化部署"
    echo "=================================="

    check_requirements
    setup_directories
    deploy_code
    build_project
    setup_environment
    setup_nginx
    setup_systemd
    start_services
    create_management_scripts

    echo ""
    echo "🎉 部署完成！"
    echo "=================================="
    echo "访问地址: http://$(hostname -I | awk '{print $1}')"
    echo "管理命令:"
    echo "  查看状态: /opt/deepsearch/status.sh"
    echo "  查看日志: /opt/deepsearch/logs.sh"
    echo "  重启服务: /opt/deepsearch/restart.sh"
    echo "  备份数据: /opt/deepsearch/backup.sh"
    echo ""
    echo "systemctl 命令:"
    echo "  sudo systemctl status deepsearch"
    echo "  sudo systemctl restart deepsearch"
    echo "  sudo systemctl stop deepsearch"
    echo ""
}

# 执行主函数
main "$@"
EOF

chmod +x deploy.sh
```

### 使用部署脚本

```bash
# 下载并执行部署脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/deepsearch/main/deploy.sh | bash

# 或者手动执行
./deploy.sh
```

## 环境变量详细说明

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `NODE_ENV` | `production` | 运行环境 |
| `PORT` | `3001` | 服务端口 |
| `LOG_LEVEL` | `info` | 日志级别 |
| `CORS_ORIGIN` | `*` | CORS 允许的源 |
| `BINARY_PATH` | `./local-agent/kwaipilot-binary` | 二进制文件路径 |
| `REPO_PATH` | `./repos` | 仓库存储路径 |
| `PROXY_URL` | `https://kwaipilot.corp.kuaishou.com/` | 代理服务地址 |
| `USER_NAME` | `production` | 用户名 |

## 性能调优建议

### 1. 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
cat >> /etc/sysctl.conf << 'EOF'
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF

sysctl -p
```

### 2. Node.js 优化

```bash
# 在 systemd 服务中添加 Node.js 优化参数
Environment="NODE_OPTIONS=--max-old-space-size=4096 --max-semi-space-size=128"
```

### 3. Nginx 优化

```nginx
# 在 nginx.conf 中添加性能优化配置
worker_processes auto;
worker_connections 4096;

# 启用 gzip
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 缓存配置
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
}
```

---

部署完成后，访问服务器 IP 地址即可使用 DeepSearch 系统。如有问题，请查看日志文件进行排查。

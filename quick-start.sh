#!/bin/bash

# DeepSearch Ubuntu 快速启动脚本
# 适用于开发和测试环境的快速部署
# 使用方法: chmod +x quick-start.sh && ./quick-start.sh start-dev

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
PORT=3001
WEB_PORT=5173

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        return 0
    else
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local timeout=${2:-30}
    local count=0
    
    log_info "等待服务启动: $url"
    
    while [ $count -lt $timeout ]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            log_success "服务已启动: $url"
            return 0
        fi
        sleep 1
        count=$((count + 1))
        printf "."
    done
    
    echo ""
    log_error "服务启动超时: $url"
    return 1
}

# 安装系统依赖
install_deps() {
    log_info "检查并安装系统依赖..."
    
    # 检查是否为 Ubuntu
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_warning "此脚本专为 Ubuntu 设计，其他系统可能需要手动调整"
    fi
    
    # 更新包列表
    if [ "$EUID" -eq 0 ]; then
        apt update
    else
        sudo apt update
    fi
    
    # 安装基础依赖
    local deps="curl wget git build-essential python3 sqlite3"
    for dep in $deps; do
        if ! command_exists $dep; then
            log_info "安装 $dep..."
            if [ "$EUID" -eq 0 ]; then
                apt install -y $dep
            else
                sudo apt install -y $dep
            fi
        fi
    done
    
    log_success "系统依赖检查完成"
}

# 安装 Node.js
install_nodejs() {
    if ! command_exists node; then
        log_info "安装 Node.js..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt install -y nodejs
    fi
    
    # 检查版本
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi
    
    # 安装 pnpm
    if ! command_exists pnpm; then
        log_info "安装 pnpm..."
        npm install -g pnpm@latest
    fi
    
    log_success "Node.js 环境准备完成: $(node -v), pnpm: $(pnpm -v)"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用..."
    
    if check_port $PORT; then
        log_error "端口 $PORT 已被占用，请先停止相关服务或修改端口"
        netstat -tlnp | grep ":$PORT "
        exit 1
    fi
    
    log_success "端口检查通过"
}

# 安装项目依赖
install_project() {
    log_info "安装项目依赖..."
    
    # 安装依赖
    pnpm install
    
    # 安装基础组件
    log_info "安装基础组件..."
    pnpm install:base
    
    # 构建项目
    log_info "构建项目..."
    pnpm build
    
    log_success "项目构建完成"
}

# 创建环境配置
setup_env() {
    log_info "创建环境配置..."
    
    cat > .env.local << 'EOF'
NODE_ENV=development
PORT=3001
LOG_LEVEL=info
CORS_ORIGIN=*
CORS_CREDENTIALS=false
BINARY_PATH=./local-agent/kwaipilot-binary
BINARY_CWD=./local-agent
REPO_PATH=./repos
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=https://kwaipilot.corp.kuaishou.com/
AGENT_PREFERENCE=intelligent
USER_NAME=dev
HEALTH_CHECK_INTERVAL=600000
HEALTH_CHECK_TIMEOUT=180000
MAX_RESTART_ATTEMPTS=10
RESTART_DELAY=5000
EOF
    
    # 创建必要目录
    mkdir -p repos data logs
    
    # 设置权限
    chmod +x local-agent/kwaipilot-binary 2>/dev/null || true
    
    log_success "环境配置完成"
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    
    cd server
    
    # 加载环境变量并启动
    export $(cat ../.env.local | xargs)
    nohup node dist/server/src/main.js > ../logs/server.log 2>&1 &
    SERVER_PID=$!
    echo $SERVER_PID > ../server.pid
    
    cd ..
    
    # 等待服务启动
    wait_for_service "http://localhost:$PORT/health" 30
    
    log_success "后端服务启动成功 (PID: $SERVER_PID)"
}

# 启动前端服务（开发模式）
start_frontend_dev() {
    log_info "启动前端开发服务..."
    
    cd web
    nohup pnpm dev > ../logs/web.log 2>&1 &
    WEB_PID=$!
    echo $WEB_PID > ../web.pid
    cd ..
    
    # 等待前端服务启动
    wait_for_service "http://localhost:$WEB_PORT" 30
    
    log_success "前端开发服务启动成功 (PID: $WEB_PID)"
}

# 使用 nginx 提供前端静态文件
start_frontend_nginx() {
    log_info "配置 Nginx 提供前端服务..."
    
    # 检查 nginx 是否安装
    if ! command_exists nginx; then
        log_info "安装 Nginx..."
        sudo apt install -y nginx
    fi
    
    # 创建临时配置
    sudo tee /etc/nginx/sites-available/deepsearch-temp > /dev/null << EOF
server {
    listen 80;
    server_name localhost;
    
    location / {
        root $(pwd)/web/dist;
        try_files \$uri \$uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://127.0.0.1:$PORT/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
    
    location /socket.io/ {
        proxy_pass http://127.0.0.1:$PORT/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
EOF
    
    # 启用配置
    sudo ln -sf /etc/nginx/sites-available/deepsearch-temp /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试并重启 nginx
    sudo nginx -t && sudo systemctl restart nginx
    
    log_success "Nginx 配置完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    # 停止后端
    if [ -f "server.pid" ]; then
        SERVER_PID=$(cat server.pid)
        if ps -p $SERVER_PID > /dev/null; then
            kill $SERVER_PID
            log_success "后端服务已停止 (PID: $SERVER_PID)"
        fi
        rm -f server.pid
    fi
    
    # 停止前端开发服务
    if [ -f "web.pid" ]; then
        WEB_PID=$(cat web.pid)
        if ps -p $WEB_PID > /dev/null; then
            kill $WEB_PID
            log_success "前端开发服务已停止 (PID: $WEB_PID)"
        fi
        rm -f web.pid
    fi
    
    # 停止相关进程
    pkill -f "kwaipilot-binary" || true
    pkill -f "deepsearch" || true
    
    log_success "所有服务已停止"
}

# 显示状态
show_status() {
    log_info "服务状态:"
    
    echo ""
    echo "=== 进程状态 ==="
    if [ -f "server.pid" ]; then
        SERVER_PID=$(cat server.pid)
        if ps -p $SERVER_PID > /dev/null; then
            echo "✅ 后端服务运行中 (PID: $SERVER_PID)"
        else
            echo "❌ 后端服务未运行"
        fi
    else
        echo "❌ 后端服务未运行"
    fi
    
    if [ -f "web.pid" ]; then
        WEB_PID=$(cat web.pid)
        if ps -p $WEB_PID > /dev/null; then
            echo "✅ 前端开发服务运行中 (PID: $WEB_PID)"
        else
            echo "❌ 前端开发服务未运行"
        fi
    else
        echo "❌ 前端开发服务未运行"
    fi
    
    echo ""
    echo "=== 端口监听 ==="
    netstat -tlnp 2>/dev/null | grep -E ":(80|$PORT|$WEB_PORT) " || echo "无相关端口监听"
    
    echo ""
    echo "=== 健康检查 ==="
    if curl -s -f "http://localhost:$PORT/health" >/dev/null 2>&1; then
        echo "✅ 后端服务健康"
    else
        echo "❌ 后端服务不健康"
    fi
    
    if curl -s -f "http://localhost/" >/dev/null 2>&1; then
        echo "✅ 前端服务健康"
    elif curl -s -f "http://localhost:$WEB_PORT/" >/dev/null 2>&1; then
        echo "✅ 前端开发服务健康"
    else
        echo "❌ 前端服务不健康"
    fi
}

# 显示帮助
show_help() {
    echo "DeepSearch Ubuntu 快速启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [start|start-dev|stop|status|help]"
    echo ""
    echo "选项:"
    echo "  start      启动生产模式 (使用 Nginx)"
    echo "  start-dev  启动开发模式 (前端开发服务器)"
    echo "  stop       停止所有服务"
    echo "  status     查看服务状态"
    echo "  help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start-dev  # 开发模式启动"
    echo "  $0 start      # 生产模式启动"
    echo "  $0 stop       # 停止服务"
}

# 主函数
main() {
    local action=${1:-start-dev}
    
    case $action in
        start)
            install_deps
            install_nodejs
            check_ports
            install_project
            setup_env
            start_backend
            start_frontend_nginx
            echo ""
            log_success "🎉 DeepSearch 启动完成！"
            echo "访问地址: http://localhost"
            echo "API 地址: http://localhost/api"
            echo "健康检查: http://localhost:$PORT/health"
            ;;
        start-dev)
            install_deps
            install_nodejs
            check_ports
            install_project
            setup_env
            start_backend
            start_frontend_dev
            echo ""
            log_success "🎉 DeepSearch 开发环境启动完成！"
            echo "前端地址: http://localhost:$WEB_PORT"
            echo "后端地址: http://localhost:$PORT"
            echo "健康检查: http://localhost:$PORT/health"
            ;;
        stop)
            stop_services
            ;;
        status)
            show_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知选项: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

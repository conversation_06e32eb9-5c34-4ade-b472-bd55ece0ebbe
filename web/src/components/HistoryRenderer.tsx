import React, { useEffect } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import mermaid from "mermaid";
import "./HistoryRenderer.css";

// 参考 VSCode 插件的 LocalMessage 类型
interface LocalMessage {
  ts: number;
  role?: "user";
  sessionId: string;
  chatId?: string;
  type: "ask" | "say";
  ask?: string;
  say?: string;
  text?: string;
  partial?: boolean;
  lastCheckpointHash?: string;
  isCheckpointCheckedOut?: boolean;
  conversationHistoryIndex?: number;
  conversationHistoryDeletedRange?: [number, number];
}

interface HistoryRendererProps {
  localMessages: LocalMessage[];
}

// 判断是否为用户消息
const isHumanMessage = (message: LocalMessage): boolean => {
  return (
    message.type === "say" && message.say === "text" && message.role === "user"
  );
};

// 检查是否为原始工具数据（需要过滤掉）
const isRawToolData = (text: string): boolean => {
  if (!text) return false;

  try {
    const parsed = JSON.parse(text);
    if (parsed && typeof parsed === "object") {
      if (
        parsed.type === "text" &&
        parsed.text &&
        typeof parsed.text === "string"
      ) {
        if (
          parsed.category === "tool-title" ||
          parsed.category === "tool-response"
        ) {
          return true;
        }
      }
      if (Array.isArray(parsed) || (parsed.toolName && parsed.params)) {
        return true;
      }
    }
  } catch {
    const jsonPattern = /^\s*[\[{].*[\]}]\s*$/s;
    if (jsonPattern.test(text)) {
      if (
        text.includes('"type":"text"') &&
        text.includes('"category":"tool-')
      ) {
        return true;
      }
    }
  }

  return false;
};

// 简化工具调用的展示
const renderSimplifiedTool = (message: LocalMessage): string => {
  const { ask, say, text } = message;

  if (ask === "tool" || say === "tool") {
    try {
      const tool = JSON.parse(text || "{}");
      return `**🔧 工具调用**: ${tool.tool || "未知工具"}${
        tool.path ? ` - ${tool.path}` : ""
      }`;
    } catch {
      return `**🔧 工具调用**: ${text || ""}`;
    }
  }

  if (ask === "command" || say === "command") {
    return `**💻 命令执行**: \`${text || ""}\``;
  }

  return text || "";
};

// 处理消息内容，简化工具调用
const processMessageContent = (message: LocalMessage): string => {
  const { type, ask, say, text } = message;

  // 过滤掉原始工具数据
  if (text && isRawToolData(text)) {
    return "";
  }

  // 简化工具调用
  if (
    ask === "tool" ||
    say === "tool" ||
    ask === "command" ||
    say === "command"
  ) {
    return renderSimplifiedTool(message);
  }

  // 简化API状态消息
  if (type === "say") {
    switch (say) {
      case "api_req_started":
        return "**🔄 正在处理请求...**";
      case "api_req_finished":
        return "**✅ 请求完成**";
      case "api_req_failed":
        return `**❌ 请求失败**: ${text || ""}`;
      case "checkpoint_created":
        return "";
      case "completion_result":
      case "error":
      case "text":
      case "task":
      default:
        return text || "";
    }
  }

  return text || "";
};

// 将消息分组为对话
const groupMessagesIntoConversations = (messages: LocalMessage[]) => {
  const conversations: {
    question: string;
    answer: string;
    timestamp: number;
  }[] = [];
  let currentQuestion = "";
  let currentAnswer: string[] = [];
  let currentTimestamp = 0;

  for (const message of messages.filter((item) => !!item.chatId)) {
    if (isHumanMessage(message)) {
      // 如果有之前的对话，先保存
      if (currentQuestion || currentAnswer.length > 0) {
        conversations.push({
          question: currentQuestion,
          answer: currentAnswer
            .filter((content) => content.trim())
            .join("\n\n"),
          timestamp: currentTimestamp,
        });
      }

      // 开始新的对话
      currentQuestion = message.text || "";
      currentAnswer = [];
      currentTimestamp = message.ts;
    } else {
      // 处理助手消息
      const content = processMessageContent(message);
      if (content.trim()) {
        currentAnswer.push(content);
      }
      if (message.ts > currentTimestamp) {
        currentTimestamp = message.ts;
      }
    }
  }

  // 保存最后一个对话
  if (currentQuestion || currentAnswer.length > 0) {
    conversations.push({
      question: currentQuestion,
      answer: currentAnswer.filter((content) => content.trim()).join("\n\n"),
      timestamp: currentTimestamp,
    });
  }

  return conversations;
};

// Mermaid 组件
const MermaidDiagram: React.FC<{ chart: string }> = React.memo(({ chart }) => {
  const [svgContent, setSvgContent] = React.useState<string>("");
  const [isLoading, setIsLoading] = React.useState<boolean>(true);

  useEffect(() => {
    let isMounted = true;

    const renderMermaid = async () => {
      try {
        setIsLoading(true);

        // 确保mermaid只初始化一次
        if (!mermaid.mermaidAPI) {
          mermaid.initialize({
            startOnLoad: false,
            theme: "default",
            securityLevel: "loose",
          });
        }

        const id = `mermaid-${Date.now()}-${Math.random()
          .toString(36)
          .substr(2, 9)}`;
        const { svg } = await mermaid.render(id, chart);

        if (isMounted) {
          setSvgContent(svg);
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Mermaid rendering error:", error);
        if (isMounted) {
          setSvgContent(
            `<div class="mermaid-error">图表渲染失败: ${
              error instanceof Error ? error.message : "未知错误"
            }</div>`
          );
          setIsLoading(false);
        }
      }
    };

    renderMermaid();

    return () => {
      isMounted = false;
    };
  }, [chart]);

  if (isLoading) {
    return (
      <div className="mermaid-diagram mermaid-loading">正在渲染图表...</div>
    );
  }

  return (
    <div
      className="mermaid-diagram"
      dangerouslySetInnerHTML={{ __html: svgContent }}
    />
  );
});

// 自定义 Markdown 组件，支持 Mermaid
const MarkdownWithMermaid: React.FC<{ content: string }> = ({ content }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={{
        code({ node, inline, className, children, ...props }) {
          const match = /language-(\w+)/.exec(className || "");
          const language = match ? match[1] : "";

          if (!inline && language === "mermaid") {
            return (
              <MermaidDiagram chart={String(children).replace(/\n$/, "")} />
            );
          }

          return (
            <code className={className} {...props}>
              {children}
            </code>
          );
        },
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export const HistoryRenderer: React.FC<HistoryRendererProps> = ({
  localMessages,
}) => {
  const conversations = groupMessagesIntoConversations(localMessages);

  if (conversations.length === 0) {
    return (
      <div className="history-empty">
        <p>此会话暂无消息</p>
      </div>
    );
  }

  return (
    <div className="history-container">
      {conversations.map((conversation, index) => (
        <div key={index} className="conversation-pair">
          {/* 问题部分 */}
          {conversation.question && (
            <div className="question-section">
              <div className="question-header">
                <span className="question-label">Q:</span>
                <span className="question-time">
                  {new Date(conversation.timestamp).toLocaleString()}
                </span>
              </div>
              <div className="question-content">
                <MarkdownWithMermaid content={conversation.question} />
              </div>
            </div>
          )}

          {/* 回答部分 */}
          {conversation.answer && (
            <div className="answer-section">
              <div className="answer-header">
                <span className="answer-label">A:</span>
              </div>
              <div className="answer-content">
                <MarkdownWithMermaid content={conversation.answer} />
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

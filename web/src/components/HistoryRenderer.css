.history-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #e8e8e8;
  background: #1e1e1e;
  min-height: 100vh;
}

.history-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #a7a7a7;
  font-size: 16px;
}

.conversation-pair {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #3c3c3c;
}

.conversation-pair:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* 问题样式 */
.question-section {
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #e8e8e8;
}


.question-label {
  font-weight: 500;
  color: #e8e8e8;
  font-size: 14px;
}

.question-time {
  margin-left: auto;
  font-size: 12px;
  color: #6b7280;
}

.question-content {
  color: #e8e8e8;
  padding: 0;
  margin-left: 24px;
}

/* 回答样式 */
.answer-section {
  margin-top: 16px;
}

.answer-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #e8e8e8;
}


.answer-label {
  font-weight: 500;
  color: #e8e8e8;
  font-size: 14px;
}

.answer-content {
  color: #e8e8e8;
  padding: 0;
  margin-left: 24px;
}

/* Markdown 内容样式 */
.question-content h1,
.question-content h2,
.question-content h3,
.question-content h4,
.question-content h5,
.question-content h6,
.answer-content h1,
.answer-content h2,
.answer-content h3,
.answer-content h4,
.answer-content h5,
.answer-content h6 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-weight: 600;
  line-height: 1.25;
  color: #e8e8e8;
}

.question-content h1,
.answer-content h1 {
  font-size: 1.5rem;
  border-bottom: 1px solid #3c3c3c;
  padding-bottom: 6px;
}

.question-content h2,
.answer-content h2 {
  font-size: 1.25rem;
}

.question-content h3,
.answer-content h3 {
  font-size: 1.125rem;
}

.question-content p,
.answer-content p {
  margin-bottom: 12px;
  color: #e8e8e8;
}

.question-content ul,
.question-content ol,
.answer-content ul,
.answer-content ol {
  margin-bottom: 12px;
  padding-left: 20px;
  color: #e8e8e8;
}

.question-content li,
.answer-content li {
  margin-bottom: 4px;
}

.question-content blockquote,
.answer-content blockquote {
  border-left: 3px solid #6b7280;
  padding-left: 12px;
  margin: 12px 0;
  color: #a7a7a7;
  font-style: italic;
}

.question-content code,
.answer-content code {
  background: #2c2c2c;
  color: #e8e8e8;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.question-content pre,
.answer-content pre {
  background: #2c2c2c;
  color: #e8e8e8;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid #3c3c3c;
}

.question-content pre code,
.answer-content pre code {
  background: transparent;
  padding: 0;
  color: inherit;
}

.question-content table,
.answer-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
}

.question-content th,
.question-content td,
.answer-content th,
.answer-content td {
  border: 1px solid #3c3c3c;
  padding: 6px 10px;
  text-align: left;
  color: #e8e8e8;
}

.question-content th,
.answer-content th {
  background: #2c2c2c;
  font-weight: 600;
}

/* Mermaid 图表样式 */
.mermaid-diagram {
  margin: 16px 0;
  text-align: center;
  background: #2c2c2c;
  border: 1px solid #3c3c3c;
  border-radius: 6px;
  padding: 12px;
}

.mermaid-diagram svg {
  max-width: 100%;
  height: auto;
}

.mermaid-error {
  color: #e8e8e8;
  background: #2c2c2c;
  border: 1px solid #3c3c3c;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
  font-size: 14px;
}

.mermaid-loading {
  color: #a7a7a7;
  background: #2c2c2c;
  border: 1px solid #3c3c3c;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
  font-size: 14px;
}

/* 工具调用简化样式 */
.answer-content p strong {
  color: #a7a7a7;
  font-weight: 500;
}

.answer-content p:has(strong:first-child) {
  background: #2c2c2c;
  border: 1px solid #3c3c3c;
  border-radius: 4px;
  padding: 6px 10px;
  margin: 6px 0;
  font-size: 0.9em;
  color: #a7a7a7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-container {
    padding: 16px;
  }
  
  .question-content,
  .answer-content {
    margin-left: 0;
    padding: 12px;
  }
  
  .question-header,
  .answer-header {
    flex-wrap: wrap;
  }
  
  .question-time {
    margin-left: 0;
    width: 100%;
    margin-top: 4px;
  }
}

# DeepSearch Ubuntu 20.04 生产环境部署指南

## 快速部署

### 一键部署脚本

```bash
# 1. 上传代码到服务器
scp -r . user@your-server:/tmp/deepsearch

# 2. 登录服务器
ssh user@your-server

# 3. 切换到代码目录并执行部署
cd /tmp/deepsearch
sudo chmod +x ubuntu-deploy.sh
sudo ./ubuntu-deploy.sh
```

## 手动部署步骤

### 1. 系统准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y curl wget git build-essential python3 python3-pip sqlite3 nginx supervisor ufw
```

### 2. 安装 Node.js 18

```bash
# 添加 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# 安装 Node.js
sudo apt install -y nodejs

# 验证安装
node -v  # 应该显示 v18.x.x
npm -v

# 安装 pnpm
sudo npm install -g pnpm@latest
```

### 3. 创建部署目录和用户

```bash
# 创建部署目录
sudo mkdir -p /opt/deepsearch
sudo useradd -r -s /bin/bash -d /opt/deepsearch deepsearch
sudo chown -R deepsearch:deepsearch /opt/deepsearch

# 创建必要的子目录
sudo mkdir -p /opt/deepsearch/{repos,data,logs,backup}
```

### 4. 部署代码

```bash
# 复制代码到部署目录
sudo cp -r /tmp/deepsearch /opt/deepsearch/source
sudo chown -R deepsearch:deepsearch /opt/deepsearch

# 切换到部署目录
cd /opt/deepsearch/source

# 安装依赖（使用 deepsearch 用户）
sudo -u deepsearch pnpm install --frozen-lockfile
sudo -u deepsearch pnpm install:base
sudo -u deepsearch pnpm build
```

### 5. 配置环境变量

```bash
sudo tee /opt/deepsearch/.env.production > /dev/null << 'EOF'
NODE_ENV=production
PORT=3001
LOG_LEVEL=info
CORS_ORIGIN=*
CORS_CREDENTIALS=false
BINARY_PATH=/opt/deepsearch/source/local-agent/kwaipilot-binary
BINARY_CWD=/opt/deepsearch/source/local-agent
REPO_PATH=/opt/deepsearch/repos
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=https://kwaipilot.corp.kuaishou.com/
AGENT_PREFERENCE=intelligent
USER_NAME=production
HEALTH_CHECK_INTERVAL=600000
HEALTH_CHECK_TIMEOUT=180000
MAX_RESTART_ATTEMPTS=10
RESTART_DELAY=5000
EOF

sudo chown deepsearch:deepsearch /opt/deepsearch/.env.production
sudo chmod 600 /opt/deepsearch/.env.production
```

### 6. 配置 Nginx

```bash
# 备份默认配置
sudo mv /etc/nginx/sites-enabled/default /etc/nginx/sites-enabled/default.backup

# 创建 DeepSearch 配置
sudo tee /etc/nginx/sites-available/deepsearch > /dev/null << 'EOF'
server {
    listen 80;
    server_name _;
    
    # 前端静态文件
    location / {
        root /opt/deepsearch/source/web/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
    }
    
    # WebSocket 支持
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3001/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:3001/health;
        access_log off;
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/deepsearch /etc/nginx/sites-enabled/
sudo nginx -t
```

### 7. 配置 Supervisor

```bash
sudo tee /etc/supervisor/conf.d/deepsearch.conf > /dev/null << 'EOF'
[program:deepsearch]
command=/usr/bin/node dist/server/src/main.js
directory=/opt/deepsearch/source/server
user=deepsearch
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/deepsearch/logs/deepsearch.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
environment=NODE_ENV=production,PORT=3001,LOG_LEVEL=info,CORS_ORIGIN=*,BINARY_PATH=/opt/deepsearch/source/local-agent/kwaipilot-binary,BINARY_CWD=/opt/deepsearch/source/local-agent,REPO_PATH=/opt/deepsearch/repos,ENABLE_REPO_INDEX=true,PROXY_URL=https://kwaipilot.corp.kuaishou.com/,USER_NAME=production
EOF
```

### 8. 配置防火墙

```bash
# 启用防火墙
sudo ufw enable

# 允许必要端口
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 查看状态
sudo ufw status
```

### 9. 启动服务

```bash
# 启动 Supervisor 服务
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start deepsearch

# 启动 Nginx
sudo systemctl enable nginx
sudo systemctl restart nginx

# 检查服务状态
sudo supervisorctl status
sudo systemctl status nginx
```

## 验证部署

```bash
# 检查端口监听
netstat -tlnp | grep -E ':(80|3001) '

# 健康检查
curl http://localhost:3001/health
curl http://localhost/

# 查看日志
sudo tail -f /opt/deepsearch/logs/deepsearch.log
```

## 日常管理

### 服务管理

```bash
# 查看服务状态
sudo supervisorctl status deepsearch
sudo systemctl status nginx

# 重启服务
sudo supervisorctl restart deepsearch
sudo systemctl restart nginx

# 停止服务
sudo supervisorctl stop deepsearch
sudo systemctl stop nginx

# 查看日志
sudo tail -f /opt/deepsearch/logs/deepsearch.log
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 更新代码

```bash
# 1. 备份当前版本
sudo cp -r /opt/deepsearch/source /opt/deepsearch/backup/source-$(date +%Y%m%d_%H%M%S)

# 2. 上传新代码
scp -r . user@your-server:/tmp/deepsearch-new

# 3. 停止服务
sudo supervisorctl stop deepsearch

# 4. 更新代码
sudo rm -rf /opt/deepsearch/source
sudo cp -r /tmp/deepsearch-new /opt/deepsearch/source
sudo chown -R deepsearch:deepsearch /opt/deepsearch/source

# 5. 重新构建
cd /opt/deepsearch/source
sudo -u deepsearch pnpm install --frozen-lockfile
sudo -u deepsearch pnpm install:base
sudo -u deepsearch pnpm build

# 6. 启动服务
sudo supervisorctl start deepsearch
```

### 备份数据

```bash
# 创建备份脚本
sudo tee /opt/deepsearch/backup.sh > /dev/null << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/deepsearch/backup"
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf $BACKUP_DIR/deepsearch-data-$DATE.tar.gz \
    /opt/deepsearch/data \
    /opt/deepsearch/repos \
    /opt/deepsearch/.env.production
echo "备份完成: $BACKUP_DIR/deepsearch-data-$DATE.tar.gz"
# 保留最近7天的备份
find $BACKUP_DIR -name "deepsearch-data-*.tar.gz" -mtime +7 -delete
EOF

sudo chmod +x /opt/deepsearch/backup.sh

# 设置定时备份
echo "0 2 * * * /opt/deepsearch/backup.sh" | sudo crontab -
```

## 性能优化

### 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化内核参数
sudo tee -a /etc/sysctl.conf > /dev/null << 'EOF'
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
vm.swappiness = 10
EOF

sudo sysctl -p
```

### Nginx 优化

```bash
# 编辑 nginx.conf
sudo nano /etc/nginx/nginx.conf

# 添加以下配置到 http 块
worker_processes auto;
worker_connections 4096;

# 启用 gzip
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查日志
   sudo tail -f /opt/deepsearch/logs/deepsearch.log
   
   # 检查端口占用
   sudo netstat -tlnp | grep 3001
   
   # 检查权限
   ls -la /opt/deepsearch/source/local-agent/kwaipilot-binary
   ```

2. **前端无法访问**
   ```bash
   # 检查 nginx 配置
   sudo nginx -t
   
   # 检查 nginx 日志
   sudo tail -f /var/log/nginx/error.log
   ```

3. **API 请求失败**
   ```bash
   # 检查后端健康状态
   curl http://localhost:3001/health
   
   # 检查 CORS 配置
   curl -H "Origin: http://localhost" http://localhost:3001/health
   ```

---

部署完成后，访问服务器 IP 地址即可使用 DeepSearch 系统。

#!/bin/bash

# 诊断二进制安装问题的脚本

set -e

VERSION="1.0.24"
DOWNLOAD_URL="https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-binary/kwaipilot-binary-${VERSION}.tar.gz"
TEMP_DIR="./temp_download"
TAR_FILE="kwaipilot-binary-${VERSION}.tar.gz"

# 显示彩色输出的函数
function echo_info() {
  echo -e "\033[1;34m[INFO]\033[0m $1"
}

function echo_success() {
  echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

function echo_error() {
  echo -e "\033[1;31m[ERROR]\033[0m $1"
}

function echo_warning() {
  echo -e "\033[1;33m[WARNING]\033[0m $1"
}

echo_info "=== DeepSearch 二进制安装诊断工具 ==="
echo_info "版本: $VERSION"
echo_info "下载URL: $DOWNLOAD_URL"
echo ""

# 1. 检查系统信息
echo_info "1. 系统信息检查"
echo "操作系统: $(uname -s)"
echo "架构: $(uname -m)"
echo "内核版本: $(uname -r)"
echo "发行版信息:"
if [ -f /etc/os-release ]; then
  cat /etc/os-release | grep -E "^(NAME|VERSION)="
elif [ -f /etc/redhat-release ]; then
  cat /etc/redhat-release
elif [ -f /etc/debian_version ]; then
  echo "Debian $(cat /etc/debian_version)"
fi
echo ""

# 2. 检查磁盘空间
echo_info "2. 磁盘空间检查"
df -h . | head -2
echo ""

# 3. 检查网络连接
echo_info "3. 网络连接检查"
if ping -c 1 cdnfile.corp.kuaishou.com >/dev/null 2>&1; then
  echo_success "可以ping通 cdnfile.corp.kuaishou.com"
else
  echo_error "无法ping通 cdnfile.corp.kuaishou.com"
fi

# 检查DNS解析
echo "DNS解析结果:"
nslookup cdnfile.corp.kuaishou.com || echo_warning "DNS解析失败"
echo ""

# 4. 检查下载工具
echo_info "4. 下载工具检查"
if command -v curl >/dev/null 2>&1; then
  echo_success "curl 可用: $(curl --version | head -1)"
else
  echo_error "curl 不可用"
fi

if command -v wget >/dev/null 2>&1; then
  echo_success "wget 可用: $(wget --version | head -1)"
else
  echo_warning "wget 不可用"
fi
echo ""

# 5. 检查tar工具
echo_info "5. tar工具检查"
if command -v tar >/dev/null 2>&1; then
  echo_success "tar 可用: $(tar --version | head -1)"
else
  echo_error "tar 不可用"
fi
echo ""

# 6. 尝试下载文件头部
echo_info "6. 尝试下载文件头部进行测试"
mkdir -p "$TEMP_DIR"

echo_info "使用curl测试下载前1KB数据..."
if curl -L --fail --connect-timeout 10 --range 0-1023 "$DOWNLOAD_URL" -o "$TEMP_DIR/test_header.bin" 2>/dev/null; then
  echo_success "curl可以连接到服务器"
  echo "文件头部信息:"
  file "$TEMP_DIR/test_header.bin" || true
  hexdump -C "$TEMP_DIR/test_header.bin" | head -3 || true
else
  echo_error "curl无法连接到服务器或下载失败"
fi
echo ""

# 7. 检查HTTP响应
echo_info "7. 检查HTTP响应头"
echo "HTTP响应头信息:"
curl -I -L --connect-timeout 10 "$DOWNLOAD_URL" 2>/dev/null || echo_error "无法获取HTTP响应头"
echo ""

# 8. 尝试完整下载并验证
echo_info "8. 尝试完整下载并验证"
echo_warning "这将下载完整文件，可能需要几分钟..."
read -p "是否继续? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  echo_info "开始下载..."
  if curl -L --fail --connect-timeout 30 --progress-bar "$DOWNLOAD_URL" -o "$TEMP_DIR/$TAR_FILE"; then
    echo_success "下载完成"
    
    # 检查文件大小
    FILE_SIZE=$(stat -c%s "$TEMP_DIR/$TAR_FILE" 2>/dev/null || stat -f%z "$TEMP_DIR/$TAR_FILE" 2>/dev/null || echo "0")
    echo "文件大小: $FILE_SIZE bytes"
    
    # 检查文件类型
    echo "文件类型: $(file "$TEMP_DIR/$TAR_FILE")"
    
    # 验证tar文件
    echo_info "验证tar文件完整性..."
    if tar -tzf "$TEMP_DIR/$TAR_FILE" >/dev/null 2>&1; then
      echo_success "tar文件验证成功"
      echo "文件内容列表:"
      tar -tzf "$TEMP_DIR/$TAR_FILE" | head -10
      if [ $(tar -tzf "$TEMP_DIR/$TAR_FILE" | wc -l) -gt 10 ]; then
        echo "... (还有更多文件)"
      fi
    else
      echo_error "tar文件验证失败"
      echo "文件头部内容:"
      hexdump -C "$TEMP_DIR/$TAR_FILE" | head -5
    fi
  else
    echo_error "下载失败"
  fi
else
  echo_info "跳过完整下载测试"
fi

# 清理
echo_info "清理临时文件..."
rm -rf "$TEMP_DIR"

echo ""
echo_info "=== 诊断完成 ==="
echo_info "如果问题仍然存在，请将以上输出发送给技术支持团队"

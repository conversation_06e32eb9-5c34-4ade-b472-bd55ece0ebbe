#!/bin/bash
set -e  # 遇到错误立即退出

OS=$(uname -s | tr '[:upper:]' '[:lower:]')
ARCH=$(uname -m)

VERSION="1.0.24"

# 设置变量
DOWNLOAD_URL="https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-binary/kwaipilot-binary-${VERSION}.tar.gz"
TEMP_DIR="./temp_download"
SUB_DIR="${OS}-${ARCH}"
TARGET_DIR="bin/agent/${SUB_DIR}"
BINARY_NAME="kwaipilot-binary"
TAR_FILE="kwaipilot-binary-${VERSION}.tar.gz"

# 显示彩色输出的函数
function echo_info() {
  echo -e "\033[1;34m[INFO]\033[0m $1"
}

function echo_success() {
  echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

function echo_error() {
  echo -e "\033[1;31m[ERROR]\033[0m $1"
}

function cleanup() {
  echo_info "清理临时文件..."
  rm -rf "$TEMP_DIR"
}

# 尝试使用wget作为备用下载工具
function try_wget_download() {
  echo_info "尝试使用wget下载..."
  if command -v wget >/dev/null 2>&1; then
    if wget --timeout=30 --tries=3 --progress=bar:force:noscroll \
       "$DOWNLOAD_URL" -O "$TEMP_DIR/$TAR_FILE"; then
      echo_success "wget下载完成!"
      return 0
    else
      echo_error "wget下载失败"
      return 1
    fi
  else
    echo_info "wget未安装，跳过wget下载尝试"
    return 1
  fi
}

# 出错时清理
trap cleanup ERR

echo_info "开始安装 kwaipilot-binary..."

# 检测是否有旧的下载临时文件并清理
if [ -d "$TEMP_DIR" ]; then
  echo_info "发现旧的临时目录，正在清理..."
  rm -rf "$TEMP_DIR"
fi
# 创建临时目录和目标目录
mkdir -p "$TEMP_DIR"
mkdir -p "$TARGET_DIR"

# 下载文件
echo_info "正在下载 $DOWNLOAD_URL..."
echo_info "这可能需要几分钟时间，取决于您的网络速度..."

# 使用 curl 下载，增加错误处理和重试
MAX_RETRIES=3
RETRY_COUNT=0
DOWNLOAD_SUCCESS=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$DOWNLOAD_SUCCESS" = false ]; do
  echo_info "尝试下载 ($((RETRY_COUNT + 1))/$MAX_RETRIES)..."

  # 清理可能存在的不完整文件
  rm -f "$TEMP_DIR/$TAR_FILE"

  if curl -L --fail --connect-timeout 30 --retry 5 --retry-delay 10 --retry-max-time 120 \
     --progress-bar --show-error "$DOWNLOAD_URL" -o "$TEMP_DIR/$TAR_FILE"; then
    DOWNLOAD_SUCCESS=true
    echo_success "下载完成!"
  else
    RETRY_COUNT=$((RETRY_COUNT + 1))
    if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
      echo_error "下载失败，正在重试 ($RETRY_COUNT/$MAX_RETRIES)..."
      echo_info "等待5秒后重试..."
      sleep 5
    else
      echo_error "curl下载尝试次数过多，尝试使用wget..."
      if try_wget_download; then
        DOWNLOAD_SUCCESS=true
        break
      else
        echo_error "所有下载方法都失败了。"
        echo_error "请检查网络连接或联系管理员。"
        echo_info "下载URL: $DOWNLOAD_URL"
        cleanup
        exit 1
      fi
    fi
  fi
done

# 验证下载的文件
echo_info "验证下载的文件..."
if [ ! -f "$TEMP_DIR/$TAR_FILE" ]; then
    echo_error "下载的文件不存在: $TEMP_DIR/$TAR_FILE"
    cleanup
    exit 1
fi

# 检查文件大小
FILE_SIZE=$(stat -c%s "$TEMP_DIR/$TAR_FILE" 2>/dev/null || stat -f%z "$TEMP_DIR/$TAR_FILE" 2>/dev/null || echo "0")
if [ "$FILE_SIZE" -lt 1000 ]; then
    echo_error "下载的文件太小 ($FILE_SIZE bytes)，可能下载不完整"
    echo_info "文件内容预览:"
    head -n 10 "$TEMP_DIR/$TAR_FILE" || true
    cleanup
    exit 1
fi

echo_info "文件大小: $FILE_SIZE bytes"

# 检查文件类型
echo_info "检查文件类型..."
file "$TEMP_DIR/$TAR_FILE" || true

# 尝试验证tar文件完整性
echo_info "验证tar文件完整性..."
if ! tar -tzf "$TEMP_DIR/$TAR_FILE" >/dev/null 2>&1; then
    echo_error "tar文件损坏或格式不正确"
    echo_info "尝试查看文件头部内容:"
    hexdump -C "$TEMP_DIR/$TAR_FILE" | head -n 5 || true
    cleanup
    exit 1
fi

# 解压文件
echo_info "正在解压文件..."
if ! tar -xzf "$TEMP_DIR/$TAR_FILE" -C "$TEMP_DIR"; then
    echo_error "解压失败!"
    echo_info "尝试使用详细模式解压以获取更多信息:"
    tar -xzvf "$TEMP_DIR/$TAR_FILE" -C "$TEMP_DIR" || true
    cleanup
    exit 1
fi

# 检查是否找到了匹配的子目录
if [ -z "$SUB_DIR" ]; then
    echo_error "无法识别您的操作系统或架构: $OS $ARCH"
    
    # 列出所有可能的目录选项
    echo_info "可用的子目录有:"
    find "$TEMP_DIR" -maxdepth 3 -type d | grep -v "^$TEMP_DIR$" | sort
    
    # 请用户选择
    echo_info "请手动输入适合您系统的子目录名称 (例如 darwin-x64):"
    read SUB_DIR
    
    if [ -z "$SUB_DIR" ]; then
        echo_error "未提供子目录，退出安装"
        cleanup
        exit 1
    fi
fi

# temp_download/kwaipilot-binary/darwin-arm64   -> bin/agent/darwin-arm64
cp -r "$TEMP_DIR/kwaipilot-binary/$SUB_DIR"/* "$TARGET_DIR/"

# 清理临时文件
cleanup




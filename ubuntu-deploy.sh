#!/bin/bash

# DeepSearch Ubuntu 20.04 生产环境部署脚本
# 适用于原生 Linux 部署，无需 Docker

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DEPLOY_DIR="/opt/deepsearch"
SERVICE_USER="deepsearch"
NODE_VERSION="18"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 sudo 运行此脚本"
        exit 1
    fi
}

# 安装系统依赖
install_system_deps() {
    log_info "更新系统包..."
    apt update

    log_info "安装系统依赖..."
    apt install -y \
        curl \
        wget \
        git \
        build-essential \
        python3 \
        python3-pip \
        sqlite3 \
        nginx \
        supervisor \
        ufw \
        htop \
        unzip

    log_success "系统依赖安装完成"
}

# 安装 Node.js
install_nodejs() {
    log_info "安装 Node.js ${NODE_VERSION}..."
    
    # 添加 NodeSource 仓库
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
    
    # 安装 Node.js
    apt install -y nodejs
    
    # 验证安装
    node_version=$(node -v)
    npm_version=$(npm -v)
    
    log_success "Node.js 安装完成: $node_version, npm: $npm_version"
    
    # 安装 pnpm
    log_info "安装 pnpm..."
    npm install -g pnpm@latest
    
    pnpm_version=$(pnpm -v)
    log_success "pnpm 安装完成: $pnpm_version"
}

# 创建服务用户
create_service_user() {
    log_info "创建服务用户..."
    
    if ! id "$SERVICE_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d $DEPLOY_DIR $SERVICE_USER
        log_success "用户 $SERVICE_USER 创建完成"
    else
        log_warning "用户 $SERVICE_USER 已存在"
    fi
}

# 创建目录结构
setup_directories() {
    log_info "创建目录结构..."
    
    mkdir -p $DEPLOY_DIR/{repos,data,logs,backup}
    chown -R $SERVICE_USER:$SERVICE_USER $DEPLOY_DIR
    chmod 755 $DEPLOY_DIR
    
    log_success "目录结构创建完成"
}

# 部署代码
deploy_code() {
    log_info "部署代码..."
    
    # 如果目录不为空，备份现有代码
    if [ "$(ls -A $DEPLOY_DIR 2>/dev/null | grep -v -E '^(repos|data|logs|backup)$')" ]; then
        log_warning "检测到现有代码，创建备份..."
        backup_dir="$DEPLOY_DIR/backup/backup-$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        cp -r $DEPLOY_DIR/* "$backup_dir/" 2>/dev/null || true
        log_info "备份保存到: $backup_dir"
    fi
    
    # 复制当前目录的代码到部署目录
    log_info "复制代码文件..."
    cp -r . $DEPLOY_DIR/source/
    
    # 切换到部署目录
    cd $DEPLOY_DIR/source
    
    # 设置权限
    chown -R $SERVICE_USER:$SERVICE_USER $DEPLOY_DIR
    
    log_success "代码部署完成"
}

# 安装项目依赖
install_project_deps() {
    log_info "安装项目依赖..."
    
    cd $DEPLOY_DIR/source
    
    # 切换到服务用户执行
    sudo -u $SERVICE_USER bash << 'EOF'
        # 安装依赖
        pnpm install --frozen-lockfile
        
        # 安装基础组件
        pnpm install:base
        
        # 构建项目
        pnpm build
EOF
    
    log_success "项目依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    cat > $DEPLOY_DIR/.env.production << 'EOF'
NODE_ENV=production
PORT=3001
LOG_LEVEL=info
CORS_ORIGIN=*
CORS_CREDENTIALS=false
BINARY_PATH=/opt/deepsearch/source/local-agent/kwaipilot-binary
BINARY_CWD=/opt/deepsearch/source/local-agent
REPO_PATH=/opt/deepsearch/repos
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=https://kwaipilot.corp.kuaishou.com/
AGENT_PREFERENCE=intelligent
USER_NAME=production
HEALTH_CHECK_INTERVAL=600000
HEALTH_CHECK_TIMEOUT=180000
MAX_RESTART_ATTEMPTS=10
RESTART_DELAY=5000
EOF
    
    chown $SERVICE_USER:$SERVICE_USER $DEPLOY_DIR/.env.production
    chmod 600 $DEPLOY_DIR/.env.production
    
    log_success "环境变量配置完成"
}

# 配置 Nginx
setup_nginx() {
    log_info "配置 Nginx..."
    
    # 备份原配置
    if [ -f /etc/nginx/sites-enabled/default ]; then
        mv /etc/nginx/sites-enabled/default /etc/nginx/sites-enabled/default.backup
    fi
    
    # 创建 DeepSearch 配置
    cat > /etc/nginx/sites-available/deepsearch << 'EOF'
server {
    listen 80;
    server_name _;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 前端静态文件
    location / {
        root /opt/deepsearch/source/web/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # HTML 文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
    }
    
    # WebSocket 支持
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3001/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:3001/health;
        access_log off;
    }
}
EOF
    
    # 启用站点
    ln -sf /etc/nginx/sites-available/deepsearch /etc/nginx/sites-enabled/
    
    # 测试配置
    nginx -t
    
    log_success "Nginx 配置完成"
}

# 配置 Supervisor
setup_supervisor() {
    log_info "配置 Supervisor..."
    
    cat > /etc/supervisor/conf.d/deepsearch.conf << 'EOF'
[program:deepsearch]
command=/usr/bin/node dist/server/src/main.js
directory=/opt/deepsearch/source/server
user=deepsearch
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/deepsearch/logs/deepsearch.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
environment=NODE_ENV=production
EOF
    
    # 添加环境变量到 supervisor 配置
    env_vars=$(cat $DEPLOY_DIR/.env.production | tr '\n' ',' | sed 's/,$//')
    sed -i "s/environment=NODE_ENV=production/environment=$env_vars/" /etc/supervisor/conf.d/deepsearch.conf
    
    log_success "Supervisor 配置完成"
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    # 启用 UFW
    ufw --force enable
    
    # 允许 SSH
    ufw allow ssh
    
    # 允许 HTTP/HTTPS
    ufw allow 80
    ufw allow 443
    
    # 显示状态
    ufw status
    
    log_success "防火墙配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 重新加载 supervisor 配置
    supervisorctl reread
    supervisorctl update
    
    # 启动 DeepSearch
    supervisorctl start deepsearch
    
    # 启动 Nginx
    systemctl enable nginx
    systemctl restart nginx
    
    # 等待服务启动
    sleep 10
    
    log_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    echo "=== Supervisor 状态 ==="
    supervisorctl status
    
    echo ""
    echo "=== Nginx 状态 ==="
    systemctl status nginx --no-pager -l
    
    echo ""
    echo "=== 端口监听 ==="
    netstat -tlnp | grep -E ':(80|3001) '
    
    echo ""
    echo "=== 健康检查 ==="
    if curl -s -f "http://localhost:3001/health" >/dev/null 2>&1; then
        log_success "后端服务健康检查通过"
    else
        log_error "后端服务健康检查失败"
    fi
    
    if curl -s -f "http://localhost/" >/dev/null 2>&1; then
        log_success "前端服务健康检查通过"
    else
        log_error "前端服务健康检查失败"
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."
    
    # 状态检查脚本
    cat > $DEPLOY_DIR/status.sh << 'EOF'
#!/bin/bash
echo "=== DeepSearch 服务状态 ==="
supervisorctl status deepsearch
echo ""
systemctl status nginx --no-pager -l
echo ""
echo "=== 端口监听 ==="
netstat -tlnp | grep -E ':(80|3001) '
echo ""
echo "=== 健康检查 ==="
curl -s http://localhost:3001/health || echo "健康检查失败"
EOF
    
    # 重启脚本
    cat > $DEPLOY_DIR/restart.sh << 'EOF'
#!/bin/bash
echo "重启 DeepSearch 服务..."
supervisorctl restart deepsearch
systemctl restart nginx
echo "服务重启完成"
EOF
    
    # 日志查看脚本
    cat > $DEPLOY_DIR/logs.sh << 'EOF'
#!/bin/bash
tail -f /opt/deepsearch/logs/deepsearch.log
EOF
    
    # 备份脚本
    cat > $DEPLOY_DIR/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/deepsearch/backup"
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf $BACKUP_DIR/deepsearch-data-$DATE.tar.gz \
    /opt/deepsearch/data \
    /opt/deepsearch/repos \
    /opt/deepsearch/.env.production
echo "备份完成: $BACKUP_DIR/deepsearch-data-$DATE.tar.gz"
find $BACKUP_DIR -name "deepsearch-data-*.tar.gz" -mtime +7 -delete
EOF
    
    chmod +x $DEPLOY_DIR/*.sh
    chown $SERVICE_USER:$SERVICE_USER $DEPLOY_DIR/*.sh
    
    log_success "管理脚本创建完成"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "🎉 DeepSearch 部署完成！"
    echo "=================================="
    echo "访问地址: http://$(hostname -I | awk '{print $1}')"
    echo "部署目录: $DEPLOY_DIR"
    echo ""
    echo "管理命令:"
    echo "  查看状态: $DEPLOY_DIR/status.sh"
    echo "  查看日志: $DEPLOY_DIR/logs.sh"
    echo "  重启服务: sudo $DEPLOY_DIR/restart.sh"
    echo "  备份数据: $DEPLOY_DIR/backup.sh"
    echo ""
    echo "Supervisor 命令:"
    echo "  sudo supervisorctl status deepsearch"
    echo "  sudo supervisorctl restart deepsearch"
    echo "  sudo supervisorctl stop deepsearch"
    echo ""
    echo "Nginx 命令:"
    echo "  sudo systemctl status nginx"
    echo "  sudo systemctl restart nginx"
    echo ""
}

# 主函数
main() {
    echo "🚀 开始 DeepSearch Ubuntu 20.04 部署..."
    echo "======================================="
    
    check_root
    install_system_deps
    install_nodejs
    create_service_user
    setup_directories
    deploy_code
    install_project_deps
    setup_environment
    setup_nginx
    setup_supervisor
    setup_firewall
    start_services
    verify_deployment
    create_management_scripts
    show_deployment_info
}

# 执行主函数
main "$@"

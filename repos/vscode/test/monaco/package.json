{"name": "test-monaco", "version": "1.0.0", "main": "index.js", "license": "MIT", "private": true, "scripts": {"compile": "node ../../node_modules/typescript/bin/tsc", "bundle-webpack": "node ../../node_modules/webpack/bin/webpack --config ./webpack.config.js --bail", "esm-check": "node esm-check/esm-check.js", "test": "node runner.js"}, "devDependencies": {"@types/chai": "^4.2.14", "chai": "^4.2.0", "warnings-to-errors-webpack-plugin": "^2.3.0"}}
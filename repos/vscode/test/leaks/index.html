<html>

<head>
	<meta charset="utf-8">
	<title>Leak Test Bed</title>
</head>

<body>
	<button id="alloc">Alloc</button>
	<button id="dealloc">Dealloc</button>

	<script src="/static/vs/loader.js"></script>
	<script>
		require.config({ baseUrl: '/static' });

		require(['vs/base/browser/event'], ({ domEvent }) => {
			let event;
			let listener;

			function alloc() {
				event = domEvent(document.body, 'mousemove');
				listener = event(e => console.log(e));
			}

			function dealloc() {
				listener.dispose();
				listener = null;
				event = null;
			}

			const allocBtn = document.getElementById('alloc');
			allocBtn.onclick = alloc;

			const deallocBtn = document.getElementById('dealloc');
			deallocBtn.onclick = dealloc;
		});
	</script>
</body>

</html>

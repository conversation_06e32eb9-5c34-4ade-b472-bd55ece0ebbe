/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
'use strict';

define({
	Sleep: {
		vkey: 'VK_SLEEP',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	WakeUp: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyA: {
		vkey: 'VK_A',
		value: 'a',
		withShift: 'A',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyB: {
		vkey: 'VK_B',
		value: 'b',
		withShift: 'B',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyC: {
		vkey: 'VK_C',
		value: 'c',
		withShift: 'C',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyD: {
		vkey: 'VK_D',
		value: 'd',
		withShift: 'D',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyE: {
		vkey: 'VK_E',
		value: 'e',
		withShift: 'E',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyF: {
		vkey: 'VK_F',
		value: 'f',
		withShift: 'F',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyG: {
		vkey: 'VK_G',
		value: 'g',
		withShift: 'G',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyH: {
		vkey: 'VK_H',
		value: 'h',
		withShift: 'H',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyI: {
		vkey: 'VK_I',
		value: 'i',
		withShift: 'I',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyJ: {
		vkey: 'VK_J',
		value: 'j',
		withShift: 'J',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyK: {
		vkey: 'VK_K',
		value: 'k',
		withShift: 'K',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyL: {
		vkey: 'VK_L',
		value: 'l',
		withShift: 'L',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyM: {
		vkey: 'VK_M',
		value: 'm',
		withShift: 'M',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyN: {
		vkey: 'VK_N',
		value: 'n',
		withShift: 'N',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyO: {
		vkey: 'VK_O',
		value: 'o',
		withShift: 'O',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyP: {
		vkey: 'VK_P',
		value: 'p',
		withShift: 'P',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyQ: {
		vkey: 'VK_Q',
		value: 'q',
		withShift: 'Q',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyR: {
		vkey: 'VK_R',
		value: 'r',
		withShift: 'R',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyS: {
		vkey: 'VK_S',
		value: 's',
		withShift: 'S',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyT: {
		vkey: 'VK_T',
		value: 't',
		withShift: 'T',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyU: {
		vkey: 'VK_U',
		value: 'u',
		withShift: 'U',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyV: {
		vkey: 'VK_V',
		value: 'v',
		withShift: 'V',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyW: {
		vkey: 'VK_W',
		value: 'w',
		withShift: 'W',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyX: {
		vkey: 'VK_X',
		value: 'x',
		withShift: 'X',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyY: {
		vkey: 'VK_Y',
		value: 'y',
		withShift: 'Y',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KeyZ: {
		vkey: 'VK_Z',
		value: 'z',
		withShift: 'Z',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit1: {
		vkey: 'VK_1',
		value: '1',
		withShift: '!',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit2: {
		vkey: 'VK_2',
		value: '2',
		withShift: '@',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit3: {
		vkey: 'VK_3',
		value: '3',
		withShift: '#',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit4: {
		vkey: 'VK_4',
		value: '4',
		withShift: '$',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit5: {
		vkey: 'VK_5',
		value: '5',
		withShift: '%',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit6: {
		vkey: 'VK_6',
		value: '6',
		withShift: '^',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit7: {
		vkey: 'VK_7',
		value: '7',
		withShift: '&',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit8: {
		vkey: 'VK_8',
		value: '8',
		withShift: '*',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit9: {
		vkey: 'VK_9',
		value: '9',
		withShift: '(',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Digit0: {
		vkey: 'VK_0',
		value: '0',
		withShift: ')',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Enter: {
		vkey: 'VK_RETURN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Escape: {
		vkey: 'VK_ESCAPE',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Backspace: {
		vkey: 'VK_BACK',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Tab: {
		vkey: 'VK_TAB',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Space: {
		vkey: 'VK_SPACE',
		value: ' ',
		withShift: ' ',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Minus: {
		vkey: 'VK_OEM_MINUS',
		value: '-',
		withShift: '_',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Equal: {
		vkey: 'VK_OEM_PLUS',
		value: '=',
		withShift: '+',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BracketLeft: {
		vkey: 'VK_OEM_4',
		value: '[',
		withShift: '{',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BracketRight: {
		vkey: 'VK_OEM_6',
		value: ']',
		withShift: '}',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Backslash: {
		vkey: 'VK_OEM_5',
		value: '\\',
		withShift: '|',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Semicolon: {
		vkey: 'VK_OEM_1',
		value: ';',
		withShift: ':',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Quote: {
		vkey: 'VK_OEM_7',
		value: '\'',
		withShift: '"',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Backquote: {
		vkey: 'VK_OEM_3',
		value: '`',
		withShift: '~',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Comma: {
		vkey: 'VK_OEM_COMMA',
		value: ',',
		withShift: '<',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Period: {
		vkey: 'VK_OEM_PERIOD',
		value: '.',
		withShift: '>',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Slash: {
		vkey: 'VK_OEM_2',
		value: '/',
		withShift: '?',
		withAltGr: '',
		withShiftAltGr: ''
	},
	CapsLock: {
		vkey: 'VK_CAPITAL',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F1: {
		vkey: 'VK_F1',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F2: {
		vkey: 'VK_F2',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F3: {
		vkey: 'VK_F3',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F4: {
		vkey: 'VK_F4',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F5: {
		vkey: 'VK_F5',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F6: {
		vkey: 'VK_F6',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F7: {
		vkey: 'VK_F7',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F8: {
		vkey: 'VK_F8',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F9: {
		vkey: 'VK_F9',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F10: {
		vkey: 'VK_F10',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F11: {
		vkey: 'VK_F11',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F12: {
		vkey: 'VK_F12',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	PrintScreen: {
		vkey: 'VK_SNAPSHOT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ScrollLock: {
		vkey: 'VK_SCROLL',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Pause: {
		vkey: 'VK_NUMLOCK',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Insert: {
		vkey: 'VK_INSERT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Home: {
		vkey: 'VK_HOME',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	PageUp: {
		vkey: 'VK_PRIOR',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Delete: {
		vkey: 'VK_DELETE',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	End: {
		vkey: 'VK_END',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	PageDown: {
		vkey: 'VK_NEXT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ArrowRight: {
		vkey: 'VK_RIGHT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ArrowLeft: {
		vkey: 'VK_LEFT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ArrowDown: {
		vkey: 'VK_DOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ArrowUp: {
		vkey: 'VK_UP',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumLock: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumpadDivide: {
		vkey: 'VK_DIVIDE',
		value: '/',
		withShift: '/',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumpadMultiply: {
		vkey: 'VK_MULTIPLY',
		value: '*',
		withShift: '*',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumpadSubtract: {
		vkey: 'VK_SUBTRACT',
		value: '-',
		withShift: '-',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumpadAdd: {
		vkey: 'VK_ADD',
		value: '+',
		withShift: '+',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumpadEnter: {
		vkey: 'VK_RETURN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad1: {
		vkey: 'VK_END',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad2: {
		vkey: 'VK_DOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad3: {
		vkey: 'VK_NEXT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad4: {
		vkey: 'VK_LEFT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad5: {
		vkey: 'VK_CLEAR',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad6: {
		vkey: 'VK_RIGHT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad7: {
		vkey: 'VK_HOME',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad8: {
		vkey: 'VK_UP',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad9: {
		vkey: 'VK_PRIOR',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Numpad0: {
		vkey: 'VK_INSERT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumpadDecimal: {
		vkey: 'VK_DELETE',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	IntlBackslash: {
		vkey: 'VK_OEM_102',
		value: '\\',
		withShift: '|',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ContextMenu: {
		vkey: 'VK_APPS',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Power: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumpadEqual: {
		vkey: 'VK_CLEAR',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F13: {
		vkey: 'VK_F13',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F14: {
		vkey: 'VK_F14',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F15: {
		vkey: 'VK_F15',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F16: {
		vkey: 'VK_F16',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F17: {
		vkey: 'VK_F17',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F18: {
		vkey: 'VK_F18',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F19: {
		vkey: 'VK_F19',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F20: {
		vkey: 'VK_F20',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F21: {
		vkey: 'VK_F21',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F22: {
		vkey: 'VK_F22',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F23: {
		vkey: 'VK_F23',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	F24: {
		vkey: 'VK_F24',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Help: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Undo: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Cut: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Copy: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Paste: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	AudioVolumeMute: {
		vkey: 'VK_VOLUME_MUTE',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	AudioVolumeUp: {
		vkey: 'VK_VOLUME_UP',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	AudioVolumeDown: {
		vkey: 'VK_VOLUME_DOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NumpadComma: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	IntlRo: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	KanaMode: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	IntlYen: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Convert: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	NonConvert: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Lang1: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Lang2: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Lang3: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Lang4: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ControlLeft: {
		vkey: 'VK_CONTROL',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ShiftLeft: {
		vkey: 'VK_SHIFT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	AltLeft: {
		vkey: 'VK_MENU',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	MetaLeft: {
		vkey: 'VK_LWIN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ControlRight: {
		vkey: 'VK_CONTROL',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	ShiftRight: {
		vkey: 'VK_SHIFT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	AltRight: {
		vkey: 'VK_MENU',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	MetaRight: {
		vkey: 'VK_RWIN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	MediaTrackNext: {
		vkey: 'VK_MEDIA_NEXT_TRACK',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	MediaTrackPrevious: {
		vkey: 'VK_MEDIA_PREV_TRACK',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	MediaStop: {
		vkey: 'VK_MEDIA_STOP',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	Eject: {
		vkey: 'VK_UNKNOWN',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	MediaPlayPause: {
		vkey: 'VK_MEDIA_PLAY_PAUSE',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	MediaSelect: {
		vkey: 'VK_LAUNCH_MEDIA_SELECT',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	LaunchMail: {
		vkey: 'VK_LAUNCH_MAIL',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	LaunchApp2: {
		vkey: 'VK_LAUNCH_APP2',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	LaunchApp1: {
		vkey: 'VK_LAUNCH_APP1',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BrowserSearch: {
		vkey: 'VK_BROWSER_SEARCH',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BrowserHome: {
		vkey: 'VK_BROWSER_HOME',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BrowserBack: {
		vkey: 'VK_BROWSER_BACK',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BrowserForward: {
		vkey: 'VK_BROWSER_FORWARD',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BrowserStop: {
		vkey: 'VK_BROWSER_STOP',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BrowserRefresh: {
		vkey: 'VK_BROWSER_REFRESH',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	},
	BrowserFavorites: {
		vkey: 'VK_BROWSER_FAVORITES',
		value: '',
		withShift: '',
		withAltGr: '',
		withShiftAltGr: ''
	}
});
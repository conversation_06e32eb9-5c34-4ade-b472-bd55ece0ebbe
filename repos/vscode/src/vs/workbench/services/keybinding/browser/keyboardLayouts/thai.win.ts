/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { KeyboardLayoutContribution } from './_.contribution.js';

KeyboardLayoutContribution.INSTANCE.registerKeyboardLayout({
	layout: { name: '0000041E', id: '', text: 'Thai Kedmanee' },
	secondaryLayouts: [],
	mapping: {
		Sleep: [],
		WakeUp: [],
		KeyA: ['ฟ', 'ฤ', '', '', 0, 'VK_A'],
		KeyB: ['ิ', 'ฺ', '', '', 0, 'VK_B'],
		KeyC: ['แ', 'ฉ', '', '', 0, 'VK_C'],
		KeyD: ['ก', 'ฏ', '', '', 0, 'VK_D'],
		KeyE: ['ำ', 'ฎ', '', '', 0, 'VK_E'],
		KeyF: ['ด', 'โ', '', '', 0, 'VK_F'],
		KeyG: ['เ', 'ฌ', '', '', 0, 'VK_G'],
		KeyH: ['้', '็', '', '', 0, 'VK_H'],
		KeyI: ['ร', 'ณ', '', '', 0, 'VK_I'],
		KeyJ: ['่', '๋', '', '', 0, 'VK_J'],
		KeyK: ['า', 'ษ', '', '', 0, 'VK_K'],
		KeyL: ['ส', 'ศ', '', '', 0, 'VK_L'],
		KeyM: ['ท', '?', '', '', 0, 'VK_M'],
		KeyN: ['ื', '์', '', '', 0, 'VK_N'],
		KeyO: ['น', 'ฯ', '', '', 0, 'VK_O'],
		KeyP: ['ย', 'ญ', '', '', 0, 'VK_P'],
		KeyQ: ['ๆ', '๐', '', '', 0, 'VK_Q'],
		KeyR: ['พ', 'ฑ', '', '', 0, 'VK_R'],
		KeyS: ['ห', 'ฆ', '', '', 0, 'VK_S'],
		KeyT: ['ะ', 'ธ', '', '', 0, 'VK_T'],
		KeyU: ['ี', '๊', '', '', 0, 'VK_U'],
		KeyV: ['อ', 'ฮ', '', '', 0, 'VK_V'],
		KeyW: ['ไ', '"', '', '', 0, 'VK_W'],
		KeyX: ['ป', ')', '', '', 0, 'VK_X'],
		KeyY: ['ั', 'ํ', '', '', 0, 'VK_Y'],
		KeyZ: ['ผ', '(', '', '', 0, 'VK_Z'],
		Digit1: ['ๅ', '+', '', '', 0, 'VK_1'],
		Digit2: ['/', '๑', '', '', 0, 'VK_2'],
		Digit3: ['-', '๒', '', '', 0, 'VK_3'],
		Digit4: ['ภ', '๓', '', '', 0, 'VK_4'],
		Digit5: ['ถ', '๔', '', '', 0, 'VK_5'],
		Digit6: ['ุ', 'ู', '', '', 0, 'VK_6'],
		Digit7: ['ึ', '฿', '', '', 0, 'VK_7'],
		Digit8: ['ค', '๕', '', '', 0, 'VK_8'],
		Digit9: ['ต', '๖', '', '', 0, 'VK_9'],
		Digit0: ['จ', '๗', '', '', 0, 'VK_0'],
		Enter: [],
		Escape: [],
		Backspace: [],
		Tab: [],
		Space: [' ', ' ', '', '', 0, 'VK_SPACE'],
		Minus: ['ข', '๘', '', '', 0, 'VK_OEM_MINUS'],
		Equal: ['ช', '๙', '', '', 0, 'VK_OEM_PLUS'],
		BracketLeft: ['บ', 'ฐ', '', '', 0, 'VK_OEM_4'],
		BracketRight: ['ล', ',', '', '', 0, 'VK_OEM_6'],
		Backslash: ['ฃ', 'ฅ', '', '', 0, 'VK_OEM_5'],
		Semicolon: ['ว', 'ซ', '', '', 0, 'VK_OEM_1'],
		Quote: ['ง', '.', '', '', 0, 'VK_OEM_7'],
		Backquote: ['_', '%', '', '', 0, 'VK_OEM_3'],
		Comma: ['ม', 'ฒ', '', '', 0, 'VK_OEM_COMMA'],
		Period: ['ใ', 'ฬ', '', '', 0, 'VK_OEM_PERIOD'],
		Slash: ['ฝ', 'ฦ', '', '', 0, 'VK_OEM_2'],
		CapsLock: [],
		F1: [],
		F2: [],
		F3: [],
		F4: [],
		F5: [],
		F6: [],
		F7: [],
		F8: [],
		F9: [],
		F10: [],
		F11: [],
		F12: [],
		PrintScreen: [],
		ScrollLock: [],
		Pause: [],
		Insert: [],
		Home: [],
		PageUp: [],
		Delete: [],
		End: [],
		PageDown: [],
		ArrowRight: [],
		ArrowLeft: [],
		ArrowDown: [],
		ArrowUp: [],
		NumLock: [],
		NumpadDivide: ['/', '/', '', '', 0, 'VK_DIVIDE'],
		NumpadMultiply: ['*', '*', '', '', 0, 'VK_MULTIPLY'],
		NumpadSubtract: ['-', '-', '', '', 0, 'VK_SUBTRACT'],
		NumpadAdd: ['+', '+', '', '', 0, 'VK_ADD'],
		NumpadEnter: [],
		Numpad1: [],
		Numpad2: [],
		Numpad3: [],
		Numpad4: [],
		Numpad5: [],
		Numpad6: [],
		Numpad7: [],
		Numpad8: [],
		Numpad9: [],
		Numpad0: [],
		NumpadDecimal: [],
		IntlBackslash: ['ฃ', 'ฅ', '', '', 0, 'VK_OEM_102'],
		ContextMenu: [],
		Power: [],
		NumpadEqual: [],
		F13: [],
		F14: [],
		F15: [],
		F16: [],
		F17: [],
		F18: [],
		F19: [],
		F20: [],
		F21: [],
		F22: [],
		F23: [],
		F24: [],
		Help: [],
		Undo: [],
		Cut: [],
		Copy: [],
		Paste: [],
		AudioVolumeMute: [],
		AudioVolumeUp: [],
		AudioVolumeDown: [],
		NumpadComma: [],
		IntlRo: [],
		KanaMode: [],
		IntlYen: [],
		Convert: [],
		NonConvert: [],
		Lang1: [],
		Lang2: [],
		Lang3: [],
		Lang4: [],
		ControlLeft: [],
		ShiftLeft: [],
		AltLeft: [],
		MetaLeft: [],
		ControlRight: [],
		ShiftRight: [],
		AltRight: [],
		MetaRight: [],
		MediaTrackNext: [],
		MediaTrackPrevious: [],
		MediaStop: [],
		Eject: [],
		MediaPlayPause: [],
		MediaSelect: [],
		LaunchMail: [],
		LaunchApp2: [],
		LaunchApp1: [],
		BrowserSearch: [],
		BrowserHome: [],
		BrowserBack: [],
		BrowserForward: [],
		BrowserStop: [],
		BrowserRefresh: [],
		BrowserFavorites: []
	}
});

#!/bin/bash

# DeepSearch 生产环境启动脚本
# 使用方法: ./start-production.sh [docker|native]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        return 0
    else
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local timeout=${2:-60}
    local count=0
    
    log_info "等待服务启动: $url"
    
    while [ $count -lt $timeout ]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            log_success "服务已启动: $url"
            return 0
        fi
        sleep 1
        count=$((count + 1))
        echo -n "."
    done
    
    echo ""
    log_error "服务启动超时: $url"
    return 1
}

# Docker 部署
deploy_docker() {
    log_info "开始 Docker 部署..."
    
    # 检查 Docker
    if ! command_exists docker; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 服务
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
    
    # 创建必要目录
    log_info "创建必要目录..."
    mkdir -p repos data logs nginx/ssl monitoring/grafana/{dashboards,datasources}
    
    # 检查配置文件
    if [ ! -f "docker-compose.production.yml" ]; then
        log_error "docker-compose.production.yml 文件不存在"
        exit 1
    fi
    
    if [ ! -f "Dockerfile.production" ]; then
        log_error "Dockerfile.production 文件不存在"
        exit 1
    fi
    
    # 构建镜像
    log_info "构建 Docker 镜像..."
    docker-compose -f docker-compose.production.yml build
    
    # 启动服务
    log_info "启动 Docker 服务..."
    docker-compose -f docker-compose.production.yml up -d
    
    # 等待服务启动
    wait_for_service "http://localhost:3001/health" 120
    wait_for_service "http://localhost" 30
    
    log_success "Docker 部署完成！"
    
    # 显示状态
    echo ""
    log_info "服务状态:"
    docker-compose -f docker-compose.production.yml ps
    
    echo ""
    log_info "访问地址:"
    echo "  前端: http://localhost"
    echo "  API: http://localhost/api"
    echo "  健康检查: http://localhost:3001/health"
    echo "  监控 (Grafana): http://localhost:3000 (admin/admin123)"
    echo "  监控 (Prometheus): http://localhost:9090"
}

# 原生部署
deploy_native() {
    log_info "开始原生部署..."
    
    # 检查 Node.js
    if ! command_exists node; then
        log_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    # 检查 Node.js 版本
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command_exists pnpm; then
        log_info "安装 pnpm..."
        npm install -g pnpm@latest
    fi
    
    # 检查端口占用
    if check_port 3001; then
        log_error "端口 3001 已被占用，请先停止相关服务"
        exit 1
    fi
    
    # 安装依赖
    log_info "安装依赖..."
    pnpm install --frozen-lockfile
    
    # 安装基础组件
    log_info "安装基础组件..."
    pnpm install:base
    
    # 构建项目
    log_info "构建项目..."
    pnpm build
    
    # 创建环境变量文件
    if [ ! -f ".env.production" ]; then
        log_info "创建环境变量文件..."
        cat > .env.production << 'EOF'
NODE_ENV=production
PORT=3001
LOG_LEVEL=info
CORS_ORIGIN=*
CORS_CREDENTIALS=false
BINARY_PATH=./local-agent/kwaipilot-binary
BINARY_CWD=./local-agent
REPO_PATH=./repos
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=https://kwaipilot.corp.kuaishou.com/
AGENT_PREFERENCE=intelligent
USER_NAME=production
HEALTH_CHECK_INTERVAL=600000
HEALTH_CHECK_TIMEOUT=180000
MAX_RESTART_ATTEMPTS=10
RESTART_DELAY=5000
EOF
    fi
    
    # 创建必要目录
    mkdir -p repos data logs
    
    # 设置权限
    chmod +x local-agent/kwaipilot-binary
    
    # 启动服务
    log_info "启动后端服务..."
    cd server
    export $(cat ../.env.production | xargs)
    nohup node dist/server/src/main.js > ../logs/server.log 2>&1 &
    SERVER_PID=$!
    echo $SERVER_PID > ../server.pid
    cd ..
    
    # 等待服务启动
    wait_for_service "http://localhost:3001/health" 60
    
    log_success "原生部署完成！"
    
    echo ""
    log_info "服务信息:"
    echo "  后端 PID: $SERVER_PID"
    echo "  日志文件: logs/server.log"
    echo "  PID 文件: server.pid"
    
    echo ""
    log_info "访问地址:"
    echo "  API: http://localhost:3001"
    echo "  健康检查: http://localhost:3001/health"
    
    echo ""
    log_info "管理命令:"
    echo "  查看日志: tail -f logs/server.log"
    echo "  停止服务: kill \$(cat server.pid)"
    echo "  重启服务: ./start-production.sh native"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    # 停止 Docker 服务
    if [ -f "docker-compose.production.yml" ]; then
        docker-compose -f docker-compose.production.yml down
    fi
    
    # 停止原生服务
    if [ -f "server.pid" ]; then
        SERVER_PID=$(cat server.pid)
        if ps -p $SERVER_PID > /dev/null; then
            kill $SERVER_PID
            log_success "后端服务已停止 (PID: $SERVER_PID)"
        fi
        rm -f server.pid
    fi
    
    # 停止相关进程
    pkill -f "kwaipilot-binary" || true
    pkill -f "deepsearch" || true
    
    log_success "所有服务已停止"
}

# 显示帮助
show_help() {
    echo "DeepSearch 生产环境启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [docker|native|stop|status|help]"
    echo ""
    echo "选项:"
    echo "  docker    使用 Docker 部署 (推荐)"
    echo "  native    使用原生方式部署"
    echo "  stop      停止所有服务"
    echo "  status    查看服务状态"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 docker     # Docker 部署"
    echo "  $0 native     # 原生部署"
    echo "  $0 stop       # 停止服务"
}

# 查看状态
show_status() {
    log_info "服务状态检查..."
    
    echo ""
    echo "=== Docker 服务 ==="
    if command_exists docker-compose && [ -f "docker-compose.production.yml" ]; then
        docker-compose -f docker-compose.production.yml ps
    else
        echo "Docker Compose 未配置"
    fi
    
    echo ""
    echo "=== 原生服务 ==="
    if [ -f "server.pid" ]; then
        SERVER_PID=$(cat server.pid)
        if ps -p $SERVER_PID > /dev/null; then
            echo "后端服务运行中 (PID: $SERVER_PID)"
        else
            echo "后端服务未运行 (PID 文件存在但进程不存在)"
        fi
    else
        echo "后端服务未运行 (无 PID 文件)"
    fi
    
    echo ""
    echo "=== 端口监听 ==="
    netstat -tlnp 2>/dev/null | grep -E ':(80|3001|3000|9090) ' || echo "无相关端口监听"
    
    echo ""
    echo "=== 健康检查 ==="
    if curl -s -f "http://localhost:3001/health" >/dev/null 2>&1; then
        echo "✅ 后端服务健康"
    else
        echo "❌ 后端服务不健康"
    fi
    
    if curl -s -f "http://localhost" >/dev/null 2>&1; then
        echo "✅ 前端服务健康"
    else
        echo "❌ 前端服务不健康"
    fi
}

# 主函数
main() {
    local action=${1:-help}
    
    case $action in
        docker)
            deploy_docker
            ;;
        native)
            deploy_native
            ;;
        stop)
            stop_services
            ;;
        status)
            show_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知选项: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

#!/bin/bash

# DeepSearch 简单启动脚本
# 使用方法: chmod +x simple-start.sh && ./simple-start.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载 nvm（如果存在）
load_nvm() {
    if [ -s "$HOME/.nvm/nvm.sh" ]; then
        export NVM_DIR="$HOME/.nvm"
        [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
        [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
        log_info "已加载 nvm"
    fi
}

# 检查 Node.js
check_nodejs() {
    # 先尝试加载 nvm
    load_nvm

    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        echo ""
        echo "请选择安装方式："
        echo "1. 使用 nvm 安装（推荐）: chmod +x install-nvm.sh && ./install-nvm.sh"
        echo "2. 手动安装 Node.js 18+"
        exit 1
    fi

    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        echo ""
        echo "如果你使用 nvm，可以运行："
        echo "  nvm install --lts"
        echo "  nvm use --lts"
        exit 1
    fi

    log_success "Node.js 版本检查通过: $(node -v)"
}

# 检查 pnpm
check_pnpm() {
    if ! command -v pnpm &> /dev/null; then
        log_info "安装 pnpm..."
        npm install -g pnpm@latest
    fi
}

# 安装依赖
install_deps() {
    if [ ! -d "node_modules" ]; then
        log_info "安装依赖..."
        pnpm install
    fi
    
    if [ ! -d "local-agent" ] || [ ! -f "local-agent/kwaipilot-binary" ]; then
        log_info "安装基础组件..."
        pnpm install:base
    fi
    
    if [ ! -d "server/dist" ] || [ ! -d "web/dist" ]; then
        log_info "构建项目..."
        pnpm build
    fi
}

# 创建环境配置
setup_env() {
    if [ ! -f ".env.local" ]; then
        log_info "创建环境配置..."
        cat > .env.local << 'EOF'
NODE_ENV=development
PORT=3001
LOG_LEVEL=info
CORS_ORIGIN=*
CORS_CREDENTIALS=false
BINARY_PATH=./local-agent/kwaipilot-binary
BINARY_CWD=./local-agent
REPO_PATH=./repos
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=https://kwaipilot.corp.kuaishou.com/
AGENT_PREFERENCE=intelligent
USER_NAME=dev
EOF
    fi
    
    # 创建必要目录
    mkdir -p repos data logs
    
    # 设置权限
    chmod +x local-agent/kwaipilot-binary 2>/dev/null || true
}

# 主函数
main() {
    log_info "🚀 DeepSearch 快速启动..."
    
    check_nodejs
    check_pnpm
    install_deps
    setup_env
    
    log_success "✅ 环境准备完成，启动开发服务器..."
    
    # 启动开发服务器
    pnpm dev
}

# 执行主函数
main "$@"

# DeepSearch 环境安装和启动指南

## 🚀 一键安装和启动

### 步骤 1: 安装 nvm 和 Node.js

```bash
# 给脚本执行权限
chmod +x install-nvm.sh

# 运行安装脚本
./install-nvm.sh

# 重新加载环境变量
source ~/.bashrc
```

### 步骤 2: 验证安装

```bash
# 检查版本
nvm --version
node -v
npm -v
pnpm -v
```

### 步骤 3: 启动项目

```bash
# 给启动脚本执行权限
chmod +x simple-start.sh

# 一键启动
./simple-start.sh
```

## 📋 详细说明

### nvm 安装脚本做了什么

`install-nvm.sh` 脚本会自动：

1. ✅ 检查系统环境和网络连接
2. ✅ 安装系统依赖（curl, wget, git, build-essential）
3. ✅ 下载并安装最新版本的 nvm
4. ✅ 配置环境变量到 ~/.bashrc 和 ~/.zshrc
5. ✅ 安装最新的 Node.js LTS 版本
6. ✅ 安装 pnpm 包管理器
7. ✅ 验证所有安装

### 启动脚本做了什么

`simple-start.sh` 脚本会自动：

1. ✅ 加载 nvm 环境
2. ✅ 检查 Node.js 版本（需要 18+）
3. ✅ 检查并安装 pnpm
4. ✅ 安装项目依赖
5. ✅ 安装基础组件
6. ✅ 构建项目
7. ✅ 创建环境配置
8. ✅ 启动开发服务器

## 🌐 访问地址

启动成功后，你可以通过以下地址访问：

- **前端页面**: `http://your-server-ip:5173`
- **后端 API**: `http://your-server-ip:3001`
- **健康检查**: `http://your-server-ip:3001/health`

## 🔧 nvm 常用命令

```bash
# 查看已安装的 Node.js 版本
nvm list

# 查看可安装的版本
nvm list-remote

# 安装最新 LTS 版本
nvm install --lts

# 安装指定版本
nvm install 18.19.0

# 切换版本
nvm use 18.19.0
nvm use --lts

# 设置默认版本
nvm alias default 18.19.0

# 查看当前版本
nvm current
```

## 🛠️ 项目管理命令

```bash
# 启动开发服务器
pnpm dev

# 构建项目
pnpm build

# 安装依赖
pnpm install

# 安装基础组件
pnpm install:base

# 清理构建文件
pnpm clean

# 运行测试
pnpm test

# 代码格式化
pnpm lint
```

## 🔄 版本管理

### 切换 Node.js 版本

```bash
# 为项目设置特定的 Node.js 版本
echo "18.19.0" > .nvmrc

# 使用项目指定的版本
nvm use

# 或者手动切换
nvm use 18.19.0
```

### 项目依赖管理

```bash
# 更新依赖
pnpm update

# 添加新依赖
pnpm add package-name

# 添加开发依赖
pnpm add -D package-name

# 删除依赖
pnpm remove package-name
```

## 🐛 故障排除

### 1. nvm 命令不存在

```bash
# 重新加载环境变量
source ~/.bashrc

# 或者重新打开终端
```

### 2. Node.js 版本不对

```bash
# 检查当前版本
node -v

# 切换到正确版本
nvm use --lts

# 设置为默认版本
nvm alias default lts/*
```

### 3. 端口被占用

```bash
# 查看端口占用
sudo netstat -tlnp | grep :3001
sudo netstat -tlnp | grep :5173

# 杀死占用进程
sudo kill -9 <PID>
```

### 4. 权限问题

```bash
# 给脚本执行权限
chmod +x *.sh

# 给二进制文件执行权限
chmod +x local-agent/kwaipilot-binary
```

### 5. 依赖安装失败

```bash
# 清理缓存
pnpm store prune

# 删除 node_modules 重新安装
rm -rf node_modules
pnpm install
```

## 📁 目录结构

```
deepsearch/
├── install-nvm.sh      # nvm 安装脚本
├── simple-start.sh     # 简单启动脚本
├── server/             # 后端代码
├── web/                # 前端代码
├── local-agent/        # 本地代理
├── repos/              # 仓库存储
├── data/               # 数据存储
├── logs/               # 日志文件
├── .env.local          # 环境变量
└── .nvmrc              # Node.js 版本文件（可选）
```

## 🎯 快速命令参考

```bash
# 完整安装流程
chmod +x install-nvm.sh && ./install-nvm.sh
source ~/.bashrc
chmod +x simple-start.sh && ./simple-start.sh

# 日常启动
nvm use --lts  # 确保使用正确版本
pnpm dev       # 启动开发服务器

# 停止服务
Ctrl + C       # 在运行 pnpm dev 的终端按 Ctrl+C
```

---

按照这个指南，你就可以轻松地在 Ubuntu 20.04 上安装和运行 DeepSearch 项目了！

#!/bin/bash

# 中国国内快速安装 nvm 脚本
# 使用方法: chmod +x install-nvm-cn.sh && ./install-nvm-cn.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 使用国内镜像源安装 nvm
install_nvm() {
    log_info "开始安装 nvm..."
    
    # 创建 nvm 目录
    export NVM_DIR="$HOME/.nvm"
    mkdir -p "$NVM_DIR"
    
    # 使用 Gitee 镜像下载 nvm
    log_info "从国内镜像下载 nvm..."
    curl -o- https://gitee.com/mirrors/nvm/raw/master/install.sh | bash
    
    # 加载 nvm
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    
    # 设置 Node.js 镜像
    log_info "配置 Node.js 国内镜像..."
    echo 'export NVM_NODEJS_ORG_MIRROR=https://npmmirror.com/mirrors/node' >> ~/.bashrc
    echo 'export NVM_NODEJS_ORG_MIRROR=https://npmmirror.com/mirrors/node' >> ~/.zshrc
    
    # 立即设置镜像
    export NVM_NODEJS_ORG_MIRROR=https://npmmirror.com/mirrors/node
    
    log_success "nvm 安装完成: $(nvm --version)"
}

# 安装 Node.js LTS 版本
install_node() {
    log_info "安装 Node.js LTS 版本..."
    nvm install --lts
    nvm use --lts
    nvm alias default node
    
    log_success "Node.js 安装完成: $(node -v)"
}

# 配置 npm 国内镜像
setup_npm_mirror() {
    log_info "配置 npm 国内镜像..."
    npm config set registry https://registry.npmmirror.com
    
    log_success "npm 镜像配置完成: $(npm config get registry)"
}

# 安装 pnpm
install_pnpm() {
    log_info "安装 pnpm..."
    npm install -g pnpm
    
    # 配置 pnpm 镜像
    pnpm config set registry https://registry.npmmirror.com
    
    log_success "pnpm 安装完成: $(pnpm -v)"
}

# 主函数
main() {
    log_info "🚀 开始安装 nvm、Node.js 和 pnpm..."
    
    install_nvm
    install_node
    setup_npm_mirror
    install_pnpm
    
    log_success "✅ 全部安装完成！"
    log_info "请运行 'source ~/.bashrc' 或重新打开终端以使用 nvm"
}

# 执行主函数
main
# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@latest

# 复制 package 文件
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY server/package.json ./server/
COPY web/package.json ./web/
COPY packages/shared/package.json ./packages/shared/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建项目
RUN pnpm build

# 生产阶段
FROM node:18-alpine AS production

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    sqlite \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# 创建应用用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S deepsearch -u 1001

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@latest

# 复制 package 文件并安装生产依赖
COPY --from=builder /app/package.json /app/pnpm-lock.yaml /app/pnpm-workspace.yaml ./
COPY --from=builder /app/server/package.json ./server/
COPY --from=builder /app/packages ./packages

# 只安装生产依赖
RUN pnpm install --prod --frozen-lockfile

# 复制构建产物
COPY --from=builder /app/server/dist ./server/dist
COPY --from=builder /app/web/dist ./web/dist
COPY --from=builder /app/local-agent ./local-agent
COPY --from=builder /app/build ./build

# 创建必要目录
RUN mkdir -p /app/repos /app/data /app/logs

# 设置权限
RUN chown -R deepsearch:nodejs /app && \
    chmod +x /app/local-agent/kwaipilot-binary

# 切换到应用用户
USER deepsearch

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# 设置环境变量
ENV NODE_ENV=production \
    PORT=3001 \
    LOG_LEVEL=info

# 启动命令
CMD ["node", "server/dist/server/src/main.js"]

# DeepSearch Ubuntu 快速部署指南

## 概述

本指南提供了在 Ubuntu 20.04 服务器上快速部署 DeepSearch 的方法，包含开发环境和生产环境两种部署方式。

## 前置要求

- Ubuntu 20.04 LTS 服务器
- 具有 sudo 权限的用户账户
- 至少 2GB 内存和 10GB 磁盘空间
- 网络连接正常

## 快速开始

### 方式一：开发环境（推荐用于测试）

```bash
# 1. 克隆或上传代码到服务器
git clone <your-repo> deepsearch
cd deepsearch

# 2. 给脚本执行权限
chmod +x quick-start.sh

# 3. 启动开发环境
./quick-start.sh start-dev
```

开发环境特点：
- 前端使用 Vite 开发服务器（热重载）
- 后端直接运行 Node.js 进程
- 适合开发和调试

访问地址：
- 前端：http://your-server-ip:5173
- 后端 API：http://your-server-ip:3001
- 健康检查：http://your-server-ip:3001/health

### 方式二：生产环境（推荐用于正式部署）

```bash
# 1. 使用完整部署脚本
chmod +x ubuntu-deploy.sh
sudo ./ubuntu-deploy.sh
```

生产环境特点：
- 使用 Nginx 提供前端静态文件
- 使用 Supervisor 管理后端进程
- 自动重启和日志管理
- 配置防火墙和安全设置

访问地址：
- 前端：http://your-server-ip
- 后端 API：http://your-server-ip/api
- 健康检查：http://your-server-ip/health

## 脚本说明

### quick-start.sh - 快速启动脚本

适用于开发和测试环境的轻量级部署：

```bash
# 启动开发环境
./quick-start.sh start-dev

# 启动生产模式（使用 Nginx）
./quick-start.sh start

# 停止所有服务
./quick-start.sh stop

# 查看服务状态
./quick-start.sh status

# 显示帮助
./quick-start.sh help
```

### ubuntu-deploy.sh - 完整部署脚本

适用于生产环境的完整部署：

```bash
# 完整部署（需要 root 权限）
sudo ./ubuntu-deploy.sh
```

自动完成：
- 安装系统依赖
- 安装 Node.js 和 pnpm
- 创建服务用户
- 配置 Nginx
- 配置 Supervisor
- 配置防火墙
- 创建管理脚本

## 服务管理

### 开发环境管理

```bash
# 查看后端日志
tail -f logs/server.log

# 查看前端日志
tail -f logs/web.log

# 手动停止服务
kill $(cat server.pid)  # 停止后端
kill $(cat web.pid)     # 停止前端
```

### 生产环境管理

```bash
# 使用 Supervisor 管理后端
sudo supervisorctl status deepsearch
sudo supervisorctl restart deepsearch
sudo supervisorctl stop deepsearch

# 使用 systemctl 管理 Nginx
sudo systemctl status nginx
sudo systemctl restart nginx
sudo systemctl stop nginx

# 查看日志
sudo tail -f /opt/deepsearch/logs/deepsearch.log
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 使用管理脚本
/opt/deepsearch/status.sh    # 查看状态
/opt/deepsearch/restart.sh   # 重启服务
/opt/deepsearch/logs.sh      # 查看日志
/opt/deepsearch/backup.sh    # 备份数据
```

## 端口说明

| 服务 | 端口 | 说明 |
|------|------|------|
| 前端（开发） | 5173 | Vite 开发服务器 |
| 前端（生产） | 80 | Nginx 静态文件服务 |
| 后端 API | 3001 | NestJS 应用服务器 |
| WebSocket | 3001 | Socket.io 连接 |

## 目录结构

### 开发环境
```
deepsearch/
├── server/          # 后端代码
├── web/             # 前端代码
├── local-agent/     # 本地代理
├── repos/           # 仓库存储
├── data/            # 数据存储
├── logs/            # 日志文件
├── .env.local       # 环境变量
├── server.pid       # 后端进程 ID
└── web.pid          # 前端进程 ID
```

### 生产环境
```
/opt/deepsearch/
├── source/          # 源代码
├── repos/           # 仓库存储
├── data/            # 数据存储
├── logs/            # 日志文件
├── backup/          # 备份文件
├── .env.production  # 生产环境变量
└── *.sh             # 管理脚本
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   sudo netstat -tlnp | grep :3001
   
   # 杀死占用进程
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   # 检查文件权限
   ls -la local-agent/kwaipilot-binary
   
   # 添加执行权限
   chmod +x local-agent/kwaipilot-binary
   ```

3. **服务无法启动**
   ```bash
   # 查看详细日志
   tail -f logs/server.log
   
   # 检查环境变量
   cat .env.local
   
   # 手动启动测试
   cd server && node dist/server/src/main.js
   ```

4. **前端无法访问后端**
   ```bash
   # 检查 CORS 配置
   curl -H "Origin: http://localhost:5173" http://localhost:3001/health
   
   # 检查防火墙
   sudo ufw status
   ```

### 日志位置

- 开发环境日志：`./logs/`
- 生产环境日志：`/opt/deepsearch/logs/`
- Nginx 日志：`/var/log/nginx/`
- 系统日志：`/var/log/syslog`

## 更新部署

### 开发环境更新
```bash
# 停止服务
./quick-start.sh stop

# 拉取最新代码
git pull

# 重新启动
./quick-start.sh start-dev
```

### 生产环境更新
```bash
# 参考 UBUNTU_DEPLOYMENT.md 中的更新代码章节
```

## 性能监控

### 系统资源监控
```bash
# 查看系统资源
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
ss -tulpn
```

### 应用监控
```bash
# 查看进程状态
ps aux | grep deepsearch

# 查看端口监听
netstat -tlnp | grep -E ':(80|3001|5173)'

# 健康检查
curl http://localhost:3001/health
```

---

选择适合你需求的部署方式，开发环境更适合快速测试，生产环境更适合正式使用。

version: '3.8'

services:
  # DeepSearch 后端服务
  deepsearch-server:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: deepsearch-server
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - LOG_LEVEL=info
      - CORS_ORIGIN=*
      - CORS_CREDENTIALS=false
      - BINARY_PATH=/app/local-agent/kwaipilot-binary
      - BINARY_CWD=/app/local-agent
      - REPO_PATH=/app/repos
      - ENABLE_REPO_INDEX=true
      - MAX_INDEX_SPACE=10
      - PROXY_URL=https://kwaipilot.corp.kuaishou.com/
      - AGENT_PREFERENCE=intelligent
      - USER_NAME=production
      - HEALTH_CHECK_INTERVAL=600000
      - HEALTH_CHECK_TIMEOUT=180000
      - MAX_RESTART_ATTEMPTS=10
      - RESTART_DELAY=5000
    volumes:
      - ./repos:/app/repos
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - deepsearch-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: deepsearch-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./web/dist:/usr/share/nginx/html:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      deepsearch-server:
        condition: service_healthy
    networks:
      - deepsearch-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis 缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: deepsearch-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - deepsearch-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: deepsearch-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - deepsearch-network

  grafana:
    image: grafana/grafana:latest
    container_name: deepsearch-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - deepsearch-network

networks:
  deepsearch-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

# 健康检查和依赖管理
x-healthcheck-defaults: &healthcheck-defaults
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s

x-logging-defaults: &logging-defaults
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

# 仓库切换优化方案

## 概述

本文档描述了 DeepSearch 项目中仓库切换时与 Kwaipilot 进程通信的优化方案。优化后的方案确保在用户切换仓库时，所有通信信息都能正确更新，避免使用过期的仓库信息。

## 问题背景

在用户切换仓库后，与 Kwaipilot 进程的通信需要完全重新初始化，确保以下信息正确更新：

1. **工作区路径 (cwd)** - 指向新选择的仓库目录
2. **仓库信息 (repo)** - 包含 Git URL、分支、提交等信息
3. **通信状态** - 确保连接正常且信息同步

## 优化方案

### 1. 分层重新初始化策略

采用两层重新初始化策略，优先使用轻量级方案：

```typescript
// 方案1: 轻量级通信重新初始化
await this.localService.reinitializeCommunication();

// 方案2: 完全重启 LocalService（备用方案）
await this.restartLocalService();
```

### 2. 核心优化点

#### 2.1 LocalService 优化

**新增公开重启方法：**
```typescript
async restart(): Promise<void> {
  this.logger.log('重启服务...');
  this.cleanup();
  await this.startSubprocess();
  this.logger.log('服务重启完成');
}
```

**新增通信重新初始化方法：**
```typescript
async reinitializeCommunication(): Promise<void> {
  // 强制更新下一条消息的公共信息
  this.messenger.forceUpdateCommonMessage();
  
  // 发送状态检查请求，传递新的仓库信息
  await this.messenger.request('state/checkRepoState', undefined);
  await this.messenger.request('state/agentState', undefined);
}
```

#### 2.2 RepoStateService 优化

**改进仓库状态管理：**
```typescript
setSelectedRepository(repoName: string, repoInfo: RepoInfo): void {
  const previousRepo = this.selectedRepoName;
  this.selectedRepoName = repoName;
  this.selectedRepoInfo = repoInfo;
  
  if (previousRepo && previousRepo !== repoName) {
    this.logger.log(`仓库切换: ${previousRepo} -> ${repoName}`);
  }
}
```

**修正工作区路径格式：**
```typescript
getSelectedRepositoryWorkspaceUri(): string | null {
  if (!this.selectedRepoName) return null;
  // 使用 ./repos/xxx 格式，与 kwaipilot 子进程通信时保持一致
  return path.resolve(process.cwd(), `./repos/${this.selectedRepoName}`);
}
```

#### 2.3 BaseMessengerService 优化

**新增强制更新公共信息方法：**
```typescript
forceUpdateCommonMessage(): void {
  // 重置时间戳，强制下次发送消息时更新公共信息
  this.lastMessageTime = 0;
}
```

### 3. 完整切换流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Agent as AgentService
    participant Repo as RepoStateService
    participant Local as LocalService
    participant KP as Kwaipilot进程

    User->>Agent: 选择新仓库
    Agent->>Repo: 更新仓库状态
    Repo-->>Agent: 返回切换信息
    
    alt 轻量级重新初始化
        Agent->>Local: reinitializeCommunication()
        Local->>Local: forceUpdateCommonMessage()
        Local->>KP: 发送状态检查请求（含新仓库信息）
        KP-->>Local: 返回状态
        Local-->>Agent: 重新初始化成功
    else 重新初始化失败
        Agent->>Local: restart()
        Local->>Local: cleanup()
        Local->>KP: 重新启动进程
        Local->>Agent: 重新设置事件处理器
    end
    
    Agent->>Agent: initAgent()
    Agent-->>User: 仓库切换完成
```

## 关键改进

### 1. 性能优化
- **优先轻量级重新初始化**：避免不必要的进程重启
- **按需重启**：只有在轻量级方案失败时才完全重启

### 2. 可靠性提升
- **双重保障**：轻量级 + 重启两种方案确保成功
- **状态验证**：通过多个请求验证通信状态
- **错误处理**：完善的异常处理和日志记录

### 3. 信息同步
- **强制更新**：确保下次通信使用最新仓库信息
- **多重验证**：通过多个 API 调用确保信息同步
- **路径一致性**：统一使用 `./repos/xxx` 格式

## 测试验证

使用提供的测试脚本验证优化效果：

```bash
node server/test-repo-switch.js
```

测试覆盖：
1. 仓库列表获取
2. 仓库选择和切换
3. 状态验证
4. 搜索功能测试
5. 多次切换测试

## 使用建议

1. **监控日志**：关注仓库切换时的日志输出
2. **性能观察**：观察轻量级重新初始化的成功率
3. **功能验证**：切换后验证搜索等功能是否正常
4. **错误处理**：注意处理切换失败的情况

## 未来改进

1. **缓存优化**：缓存仓库信息减少重复获取
2. **并发处理**：支持并发仓库切换请求
3. **状态持久化**：持久化仓库切换状态
4. **健康检查**：增强通信健康检查机制

/**
 * 测试仓库切换功能
 * 验证切换仓库后 kwaipilot 进程通信是否正确重新初始化
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testRepoSwitch() {
  console.log('🧪 开始测试仓库切换功能...\n');

  try {
    // 1. 获取可用仓库列表
    console.log('1. 获取仓库列表...');
    const reposResponse = await axios.get(`${BASE_URL}/api/repos`);
    const repos = reposResponse.data;
    console.log(`   找到 ${repos.length} 个仓库:`, repos.map(r => r.name).join(', '));

    if (repos.length < 2) {
      console.log('❌ 需要至少2个仓库来测试切换功能');
      return;
    }

    // 2. 选择第一个仓库
    const firstRepo = repos[0].name;
    console.log(`\n2. 选择第一个仓库: ${firstRepo}`);
    await axios.post(`${BASE_URL}/api/agent/select-repository`, { repoName: firstRepo });
    console.log('   ✅ 第一个仓库选择成功');

    // 3. 检查第一个仓库的状态
    console.log('\n3. 检查第一个仓库状态...');
    const firstStatus = await axios.get(`${BASE_URL}/api/agent/status`);
    console.log('   仓库状态:', firstStatus.data);

    // 4. 等待一段时间
    console.log('\n4. 等待 2 秒...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 5. 切换到第二个仓库
    const secondRepo = repos[1].name;
    console.log(`\n5. 切换到第二个仓库: ${secondRepo}`);
    await axios.post(`${BASE_URL}/api/agent/select-repository`, { repoName: secondRepo });
    console.log('   ✅ 第二个仓库选择成功');

    // 6. 检查第二个仓库的状态
    console.log('\n6. 检查第二个仓库状态...');
    const secondStatus = await axios.get(`${BASE_URL}/api/agent/status`);
    console.log('   仓库状态:', secondStatus.data);

    // 7. 验证工作区路径是否正确更新
    console.log('\n7. 验证工作区路径更新...');
    if (secondStatus.data.selectedRepository === secondRepo) {
      console.log('   ✅ 仓库切换成功');
    } else {
      console.log('   ❌ 仓库切换失败');
    }

    // 8. 测试搜索功能是否正常
    console.log('\n8. 测试搜索功能...');
    try {
      const searchResponse = await axios.post(`${BASE_URL}/api/search`, {
        query: 'function',
        topK: 5
      });
      console.log('   ✅ 搜索功能正常，返回结果数量:', searchResponse.data.length || 0);
    } catch (searchError) {
      console.log('   ⚠️ 搜索功能异常:', searchError.message);
    }

    // 9. 再次切换回第一个仓库
    console.log(`\n9. 切换回第一个仓库: ${firstRepo}`);
    await axios.post(`${BASE_URL}/api/agent/select-repository`, { repoName: firstRepo });
    console.log('   ✅ 切换回第一个仓库成功');

    // 10. 最终状态检查
    console.log('\n10. 最终状态检查...');
    const finalStatus = await axios.get(`${BASE_URL}/api/agent/status`);
    console.log('    最终仓库状态:', finalStatus.data);

    console.log('\n🎉 仓库切换测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   响应数据:', error.response.data);
    }
  }
}

// 运行测试
if (require.main === module) {
  testRepoSwitch();
}

module.exports = { testRepoSwitch };

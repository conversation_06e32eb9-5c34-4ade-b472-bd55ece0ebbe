import { Injectable, OnM<PERSON>ule<PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { spawn, ChildProcessWithoutNullStreams } from 'child_process';
import * as net from 'net';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '../config/config.service';
import { ProjectService } from './project.service';
import { RepoStateService } from './repo-state.service';

// 协议类型定义 - 参考 VSCode 插件的完整协议定义
interface Message<T = any> {
  messageType: string;
  messageId: string;
  data: T;
  common?: any;
}

// 响应基础类型
export interface ResponseBase<T> {
  status: "ok" | "failed";
  message?: string;
  data?: T;
  code?: number;
}

// IDE 设置类型
interface IdeSettings {
  dirPath: string;
  fileRetryTime: number;
  modelRetryTime: number;
  enableRepoIndex: boolean;
  agentPreference: string;
  maxIndexSpace: number;
  proxyUrl: string;
}

// 搜索参数类型
interface SearchSearchParams {
  query: string;
  chatHistory?: Array<{ role: "user" | "assistant"; content: string }>;
  topK?: number;
  limit?: number;
  targetDirectory?: string[];
}

// 仓库状态枚举
enum RepoStatusEnum {
  SUCCESS = "0",
  MAX_INDEX_SIZE_EXCEEDED_5K = "1",
  MAX_INDEX_SIZE_EXCEEDED_10K = "2",
  INDEX_FAILED = "9999",
  NOT_GIT_REPO = "11",
}

// IDE 到 Core 的协议（IDE 接收的消息）
interface ToIdeProtocol {
  'state/ideState': [undefined, ResponseBase<null>];
  'config/getIdeSetting': [undefined, ResponseBase<IdeSettings>];
  'index/progress': [
    {
      progress: number;
      total: number;
      done: number;
      filepath: string;
      action: "INDEX_FILE" | "PROCESS_FILE" | "ERROR";
      message?: string;
    },
    undefined
  ];
  'state/sendNotification': [
    {
      type: "error" | "warning" | "info";
      name: string;
      message: string;
    },
    undefined
  ];
  'state/ideInfo': [undefined, ResponseBase<{
    pluginVersion: string;
    version: string;
    platform: string;
    repoInfo: {
      git_url: string;
      dir_path: string;
      commit: string;
      branch: string;
    };
    userInfo: {
      name: string;
    };
    proxyUrl: string;
    maxIndexSpace: number;
    cwd: string;
    device?: string;
  }>];
  'assistant/agent/message': [any, void];
  'assistant/agent/messageList': [any[], void];
  'assistant/agent/apiConversationList': [any[], void];
  'assistant/agent/environment': [{ includeFileDetails: boolean }, ResponseBase<string>];
  'assistant/agent/executeCommand': [{ command: string; is_background: boolean }, ResponseBase<any>];
  'assistant/agent/editFile': [any, ResponseBase<any>];
  'assistant/agent/writeToFile': [{ path: string; content: string; newFile: boolean }, Promise<ResponseBase<any>>];
}

// Core 到 IDE 的协议（IDE 发送的消息）
interface FromIdeProtocol {
  'state/agentState': [undefined, ResponseBase<null>];
  'state/userLogin': [{ username: string }, ResponseBase<null>];
  'index/file': [{ file: string; action: "modify" | "delete" | "create" }, ResponseBase<null>];
  'index/build': [undefined, ResponseBase<boolean>];
  'index/pause': [undefined, ResponseBase<boolean>];
  'index/clearIndex': [undefined, ResponseBase<boolean>];
  'index/repoIndex': [undefined, ResponseBase<boolean>];
  'state/checkRepoState': [undefined, ResponseBase<{
    id: number;
    repo: string;
    repoPath: string;
    branch: string;
    commitId: string;
    lastUpdateTime: number;
    createTime: number;
    total: number;
    done: number;
    progress: number;
    isBuilding: boolean;
    status: RepoStatusEnum;
    isPaused: boolean;
    message: string;
  } | null>];
  'search/search': [SearchSearchParams, ResponseBase<any>];
  'assistant/agent/local': [any, ResponseBase<null>];
  'assistant/agent/getDiffSet': [{ sessionId: string; lhsHash: string; rhsHash?: string }, ResponseBase<any[]>];
  'rules/getRulesList': [{ rules: string[] }, ResponseBase<string[]>];
}

/**
 * 数据缓冲处理器 - 参考 VSCode 插件的实现
 */
class BufferDataHandle {
  private _unfinishedLine = Buffer.alloc(0);
  private static readonly MAX_BUFFER_SIZE = 1024 * 1024 * 100; // 100MB 限制

  handleData(data: Buffer, onLine: (line: string) => void) {
    if (!Buffer.isBuffer(data) || data.length === 0) {
      return;
    }

    // 检查缓冲区大小限制
    if (this._unfinishedLine.length + data.length > BufferDataHandle.MAX_BUFFER_SIZE) {
      console.error("Buffer size exceeds limit, clearing unfinished line");
      this._unfinishedLine = Buffer.alloc(0);
      return;
    }

    const chunks: Buffer[] = [];
    let start = 0;

    // 处理未完成的行
    if (this._unfinishedLine.length > 0) {
      data = Buffer.concat([this._unfinishedLine, data]);
      this._unfinishedLine = Buffer.alloc(0);
    }

    // 查找所有的 \r\n = [13, 10]，处理所有的完整行
    for (let i = 0; i < data.length - 1; i++) {
      if (data[i] === 13 && data[i + 1] === 10) {
        const chunk = data.subarray(start, i);
        chunks.push(chunk);
        start = i + 2;
        i++;
      }
    }

    // 处理剩余的数据（不完整的行）
    if (start < data.length) {
      if (data.length >= 2 && data[data.length - 2] === 13 && data[data.length - 1] === 10) {
        const remainingChunk = data.subarray(start, data.length - 2);
        if (remainingChunk.length > 0) {
          chunks.push(remainingChunk);
        }
      } else {
        this._unfinishedLine = Buffer.from(data.subarray(start));
      }
    }

    // 处理所有完整的行
    for (const chunk of chunks) {
      if (this.isValidUtf8(chunk)) {
        const line = chunk.toString("utf8");
        if (line.length > 0) {
          onLine(line);
        }
      } else {
        console.error("Invalid UTF-8 sequence detected, skipping chunk", chunk);
      }
    }
  }

  private isValidUtf8(buffer: Buffer): boolean {
    try {
      const decoder = new TextDecoder("utf-8", { fatal: true });
      decoder.decode(buffer);
      return true;
    } catch {
      return false;
    }
  }

  clear(): void {
    this._unfinishedLine = Buffer.alloc(0);
  }
}

/**
 * 消息服务基类 - 参考 VSCode 插件的 IPCMessengerBase 实现
 */
abstract class BaseMessengerService {
  protected readonly logger = new Logger(this.constructor.name);
  protected typeListeners = new Map<string, ((message: Message) => any)[]>();
  protected idListeners = new Map<string, (message: Message) => any>();
  protected lastMessageTime = Date.now();
  private errorHandlers: ((err: any, details?: string) => void)[] = [];
  private readonly dataHandler = new BufferDataHandle();

  constructor(
    protected readonly projectService: ProjectService,
    protected readonly repoStateService: RepoStateService
  ) {}

  onError(handler: (err: any, details?: string) => void) {
    this.errorHandlers.push(handler);
  }

  protected triggerError(err: any, details?: string) {
    this.errorHandlers.forEach(handler => handler(err, details));
  }

  protected handleData(data: Buffer) {
    this.lastMessageTime = Date.now();
    this.dataHandler.handleData(data, line => this.handleLine(line));
  }

  private handleLine(line: string) {
    try {
      const msg: Message = JSON.parse(line);
      if (msg.messageType === undefined || msg.messageId === undefined) {
        throw new Error("Invalid message sent: " + JSON.stringify(msg));
      }

      this.logger.debug(`📨 收到消息: ${msg.messageType} (ID: ${msg.messageId})`);
       console.log('消息',msg.data)

      // 处理注册的消息类型监听器
      const listeners = this.typeListeners.get(msg.messageType);
      listeners?.forEach(async (handler) => {
        try {
          const response = await handler(msg);
          if (response && typeof response[Symbol.asyncIterator] === "function") {
            // 处理异步迭代器（流式响应）
            for await (const update of response) {
              this.send(msg.messageType as keyof FromIdeProtocol, update, msg.messageId);
            }
            this.send(msg.messageType as keyof FromIdeProtocol, { done: true }, msg.messageId);
          } else {
            this.send(msg.messageType as keyof FromIdeProtocol, response || {}, msg.messageId);
          }
        } catch (e: any) {
          this.logger.error(`处理消息错误: ${msg.messageType}`, e);
          this.triggerError(e);
          this.send(msg.messageType as keyof FromIdeProtocol, { status: "error", message: e.message }, msg.messageId);
        }
      });

      // 处理等待响应的消息ID监听器
      const idHandler = this.idListeners.get(msg.messageId);
      if (idHandler) {
        idHandler(msg);
        this.idListeners.delete(msg.messageId);
      }
    } catch (e: any) {
      let truncatedLine = line;
      if (line.length > 200) {
        truncatedLine = line.substring(0, 100) + "..." + line.substring(line.length - 100);
      }
      this.logger.error("解析消息失败:", truncatedLine, e);
      this.triggerError(e, "解析消息失败: " + line);
    }
  }

  abstract _sendMsg(message: Message): void;

  on<T extends keyof ToIdeProtocol>(
    messageType: T,
    handler: (message: Message<ToIdeProtocol[T][0]>) => Promise<ToIdeProtocol[T][1]> | ToIdeProtocol[T][1]
  ): void {
    const msgType = messageType as string;
    this.logger.debug(`📝 注册处理器: ${msgType}`);

    if (!this.typeListeners.has(msgType)) {
      this.typeListeners.set(msgType, []);
    }
    this.typeListeners.get(msgType)?.push(handler);
  }

  send<T extends keyof FromIdeProtocol>(
    messageType: T,
    data: FromIdeProtocol[T][0],
    messageId?: string
  ): string {
    messageId = messageId ?? uuidv4();
    const msg: Message = {
      messageType: messageType as string,
      data,
      messageId,
    };
    this._sendMsg(msg);
    return messageId;
  }

  request<T extends keyof FromIdeProtocol>(
    messageType: T,
    data: FromIdeProtocol[T][0]
  ): Promise<FromIdeProtocol[T][1]> {
    const messageId = uuidv4();
    return new Promise((resolve) => {
      const handler = (msg: Message) => {
        resolve(msg.data);
        this.idListeners.delete(messageId);
      };
      this.idListeners.set(messageId, handler);
      this.send(messageType, data, messageId);
    });
  }

  invoke<T extends keyof ToIdeProtocol>(messageType: T, data: ToIdeProtocol[T][0]): ToIdeProtocol[T][1] {
    return this.typeListeners.get(messageType as string)?.[0]?.({
      messageId: uuidv4(),
      messageType: messageType as string,
      data,
    });
  }

  checkIsRunning(): boolean {
    return Date.now() - this.lastMessageTime < 5000;
  }

  /**
   * 强制更新下一条消息的公共信息（用于仓库切换后立即生效）
   */
  forceUpdateCommonMessage(): void {
    // 这个标记会在下次发送消息时强制更新公共信息
    this.lastMessageTime = 0;
  }

  dispose() {
    this.typeListeners.clear();
    this.idListeners.clear();
    this.dataHandler.clear();
  }
}

/**
 * 消息服务 - 处理与二进制服务的通信
 */
class MessengerService extends BaseMessengerService {
  constructor(
    private readonly subprocess: ChildProcessWithoutNullStreams,
    projectService: ProjectService,
    repoStateService: RepoStateService
  ) {
    super(projectService, repoStateService);
    this.setupListeners();
  }

  private setupListeners() {
    this.subprocess.stdout.on("data", (data: Buffer) => {
      this.handleData(data);
    });

    this.subprocess.stderr.on('data', (data) => {
      this.logger.error(`❌ 二进制服务错误: ${data.toString()}`);
    });

    this.subprocess.on('close', (code) => {
      this.logger.warn(`⚠️ 二进制服务退出，代码: ${code}`);
    });
  }

  _sendMsg(msg: Message) {
    if (!msg.common) {
      msg.common = this.getCommonMessage();
    }
    const jsonMsg = JSON.stringify(msg) + '\r\n';
    this.subprocess.stdin.write(jsonMsg);
    this.logger.debug(`📤 发送消息: ${msg.messageType} (ID: ${msg.messageId})`);
  }

  private getCommonMessage() {
    // 使用 RepoStateService 获取选中仓库的信息
    const repoInfo = this.repoStateService.getRepoInfoForCommunication();
    const workspaceUri = this.repoStateService.getSelectedRepositoryWorkspaceUri();

    return {
      version: "1.0.0",
      platform: "vscode",
      cwd: workspaceUri || process.cwd(),
      device: "kwaipilot-vscode",
      repo: repoInfo,
    };
  }
}

/**
 * TCP消息服务 - 开发环境下通过TCP连接到本地代理服务
 */
class TcpMessengerService extends BaseMessengerService {
  private port: number;
  private host: string;
  private socket: net.Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // 1 second
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isConnected = false;
  private requestPending: {
    messageType: string;
    data: any;
    messageId: string;
  }[] = [];

  constructor(
    projectService: ProjectService,
    repoStateService: RepoStateService,
    private configService?: ConfigService
  ) {
    super(projectService, repoStateService);
    // 使用配置服务或环境变量
    this.port =
      this.configService?.getTcpAgentPort() ||
      (process.env.KWAIPILOT_AGENT_PORT
        ? parseInt(process.env.KWAIPILOT_AGENT_PORT)
        : 3000);
    this.host = this.configService?.getTcpAgentHost() || "127.0.0.1";
    this.connect();
  }

  _sendMsg(msg: Message) {
    if (!msg.common) {
      msg.common = this.getCommonMessage();
    }

    if (this.isConnected && this.socket) {
      const jsonMsg = JSON.stringify(msg) + '\r\n';
      this.socket.write(jsonMsg);
      this.logger.debug(`📤 发送TCP消息: ${msg.messageType} (ID: ${msg.messageId})`);
    } else {
      this.logger.warn(`TCP未连接，消息加入待发送队列: ${msg.messageType}`);
      this.requestPending.push({
        messageType: msg.messageType,
        data: msg.data,
        messageId: msg.messageId
      });
    }
  }

  private connect() {
    if (this.socket) {
      this.socket.destroy();
      this.socket = null;
    }

    const client = new net.Socket();

    client.on("connect", () => {
      this.logger.log(`TCP连接成功: ${this.host}:${this.port}`);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.socket = client;

      // 处理待发送的请求
      this.requestPending.forEach((request) => {
        this.send(request.messageType as keyof FromIdeProtocol, request.data, request.messageId);
      });
      this.requestPending = [];
    });

    client.on("data", (data: Buffer) => {
      this.handleData(data);
    });

    client.on("end", () => {
      this.logger.warn("TCP连接断开");
      this.isConnected = false;
      this.handleDisconnect();
    });

    client.on("error", (err: Error) => {
      this.logger.error("TCP连接错误:", err.message);
      this.isConnected = false;
      this.handleDisconnect();
    });

    client.connect(this.port, this.host);
  }

  private handleDisconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      this.logger.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      this.reconnectTimer = setTimeout(() => {
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      this.logger.error("达到最大重连次数");
      this.triggerError(new Error("TCP连接失败，达到最大重连次数"));
    }
  }

  dispose() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    if (this.socket) {
      this.socket.destroy();
      this.socket = null;
    }
    this.isConnected = false;
    super.dispose();
  }

  private getCommonMessage() {
    // 使用 RepoStateService 获取选中仓库的信息
    const repoInfo = this.repoStateService.getRepoInfoForCommunication();
    const workspaceUri = this.repoStateService.getSelectedRepositoryWorkspaceUri();

    return {
      version: "1.0.0",
      platform: "vscode",
      cwd: workspaceUri || process.cwd(),
      device: "kwaipilot-vscode",
      repo: repoInfo,
    };
  }
}

@Injectable()
export class LocalService implements OnModuleDestroy {
  private messenger: BaseMessengerService | null = null;
  private subprocess: ChildProcessWithoutNullStreams | null = null;
  private isShuttingDown = false;
  private readonly logger = new Logger('LocalService');
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly projectService: ProjectService,
    private readonly repoStateService: RepoStateService,
  ) {}

  async onModuleInit() {
    await this.startSubprocess();
    this.startHealthCheck();
  }

  onModuleDestroy() {
    this.isShuttingDown = true;
    this.stopHealthCheck();
    this.cleanup();
  }

  private async startSubprocess() {
    try {
      // 检查是否为开发环境
      if (process.env.NODE_ENV === 'development') {
        this.logger.log('开发环境：使用TCP连接到本地代理服务');
        this.messenger = new TcpMessengerService(this.projectService, this.repoStateService, this.configService);
      } else {
        // 生产环境：启动二进制服务
        const binaryPath = this.configService.getBinaryPath();
        const cwd = this.configService.getBinaryCwd();

        this.logger.log(`启动二进制服务: ${binaryPath}`);
        this.subprocess = spawn(binaryPath, {
          env: {
            ...process.env,
            PWD: cwd,
          },
          cwd,
        });

        this.subprocess.stdout.on('data', (data) => {
          this.logger.debug(`服务输出: ${data}`);
        });

        this.subprocess.stderr.on('data', (data) => {
          this.logger.error(`服务错误: ${data}`);
        });

        this.messenger = new MessengerService(this.subprocess, this.projectService, this.repoStateService);
      }

      if (this.messenger) {
        this.messenger.onError((err, details) => {
          this.logger.error('Messenger错误:', err, details);
        });
      }

      this.logger.log('服务启动成功');
    } catch (error) {
      this.logger.error('服务启动失败', error);
      throw error;
    }
  }

  private startHealthCheck() {
    if (process.env.NODE_ENV === 'development') return;
    this.healthCheckInterval = setInterval(() => {
      if (!this.isShuttingDown && !this.checkIsRunning()) {
        this.logger.warn('服务健康检查失败，尝试重启...');
        this.restartInternal();
      }
    }, 30000); // 30秒检查一次
  }

  private stopHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * 重启服务 - 公开方法，用于仓库切换时重新初始化
   */
  async restart(): Promise<void> {
    this.logger.log('重启服务...');
    this.cleanup();
    await this.startSubprocess();
    this.logger.log('服务重启完成');
  }

  /**
   * 私有重启方法 - 用于内部健康检查
   */
  private async restartInternal() {
    this.logger.log('内部重启服务...');
    this.cleanup();
    await this.startSubprocess();
  }

  private cleanup() {
    if (this.messenger) {
      this.messenger.dispose();
      this.messenger = null;
    }

    if (this.subprocess) {
      this.subprocess.kill();
      this.subprocess = null;
    }
  }

  // 消息处理方法
  onMessage<T extends keyof ToIdeProtocol>(
    messageType: T,
    handler: (message: Message<ToIdeProtocol[T][0]>) => Promise<ToIdeProtocol[T][1]> | ToIdeProtocol[T][1]
  ) {
    this.logger.debug(`🔧 注册消息处理器: ${messageType as string}`);
    if (!this.messenger) {
      this.logger.warn(`⚠️ Messenger 未初始化，无法注册处理器: ${messageType as string}`);
      return;
    }
    this.messenger.on(messageType, handler);
  }

  sendMessage<T extends keyof FromIdeProtocol>(
    messageType: T,
    data: FromIdeProtocol[T][0],
    messageId?: string
  ): string {
    if (!this.messenger) throw new Error("服务未初始化");
    return this.messenger.send(messageType, data, messageId);
  }

  request<T extends keyof FromIdeProtocol>(
    messageType: T,
    data: FromIdeProtocol[T][0]
  ): Promise<FromIdeProtocol[T][1]> {
    if (!this.messenger) throw new Error("服务未初始化");
    return this.messenger.request(messageType, data);
  }

  checkIsRunning(): boolean {
    return !!this.messenger?.checkIsRunning();
  }

  /**
   * 重新初始化通信 - 用于仓库切换后确保通信信息正确
   */
  async reinitializeCommunication(): Promise<void> {
    if (!this.messenger) {
      this.logger.warn('Messenger 未初始化，无法重新初始化通信');
      return;
    }

    this.logger.log('重新初始化与 kwaipilot 进程的通信...');

    try {
      // 强制更新下一条消息的公共信息
      this.messenger.forceUpdateCommonMessage();

      // 发送新的仓库信息到 kwaipilot 进程
      const repoInfo = this.repoStateService.getRepoInfoForCommunication();
      const workspaceUri = this.repoStateService.getSelectedRepositoryWorkspaceUri();

      this.logger.log(`更新工作区路径: ${workspaceUri}`);
      this.logger.log(`更新仓库信息:`, repoInfo);

      // 发送状态检查请求，确保连接正常并传递新的仓库信息
      await this.messenger.request('state/checkRepoState', undefined);

      // 再次发送IDE状态信息，确保kwaipilot进程获得最新的工作区信息
      await this.messenger.request('state/agentState', undefined);

      this.logger.log('通信重新初始化完成');
    } catch (error) {
      this.logger.error('重新初始化通信失败', error);
      throw error;
    }
  }
}
